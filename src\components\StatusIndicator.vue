<template>
  <div 
    v-if="showStatus" 
    class="fixed top-4 right-4 bg-gray-900/90 backdrop-blur-sm text-white p-4 rounded-lg shadow-lg z-50 max-w-sm"
  >
    <div class="flex items-center justify-between mb-3">
      <h3 class="text-lg font-semibold text-green-400">系统状态</h3>
      <button 
        @click="showStatus = false"
        class="text-gray-400 hover:text-white transition-colors"
      >
        ✕
      </button>
    </div>
    
    <div class="space-y-2 text-sm">
      <div class="flex items-center justify-between">
        <span>Three.js场景:</span>
        <span :class="sceneStatus ? 'text-green-400' : 'text-red-400'">
          {{ sceneStatus ? '✓ 正常' : '✗ 错误' }}
        </span>
      </div>
      
      <div class="flex items-center justify-between">
        <span>监测站点:</span>
        <span class="text-blue-400">{{ stationCount }} 个</span>
      </div>
      
      <div class="flex items-center justify-between">
        <span>预警区域:</span>
        <span class="text-yellow-400">{{ warningCount }} 个</span>
      </div>
      
      <div class="flex items-center justify-between">
        <span>活跃预警:</span>
        <span class="text-orange-400">{{ activeWarningCount }} 个</span>
      </div>
      
      <div class="flex items-center justify-between">
        <span>数据更新:</span>
        <span :class="dataUpdateStatus ? 'text-green-400' : 'text-red-400'">
          {{ dataUpdateStatus ? '✓ 正常' : '✗ 停止' }}
        </span>
      </div>
      
      <div class="flex items-center justify-between">
        <span>渲染FPS:</span>
        <span class="text-cyan-400">{{ fps }} fps</span>
      </div>
    </div>
    
    <div class="mt-3 pt-3 border-t border-gray-700">
      <div class="text-xs text-gray-400">
        最后更新: {{ lastUpdateTime }}
      </div>
    </div>
  </div>
  
  <!-- 状态切换按钮 -->
  <button
    v-if="!showStatus"
    @click="showStatus = true"
    class="fixed top-4 right-4 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-lg z-50 transition-colors"
    title="显示系统状态"
  >
    📊
  </button>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import type { WeatherWarningArea, MonitoringStation, ControlPanelState } from '@/types/weather'

// Props
interface Props {
  stations: MonitoringStation[]
  warnings: WeatherWarningArea[]
  controlState: ControlPanelState
}

const props = defineProps<Props>()

// 状态
const showStatus = ref(false)
const sceneStatus = ref(true)
const dataUpdateStatus = ref(true)
const fps = ref(60)
const lastUpdateTime = ref('')

// 计算属性
const stationCount = computed(() => props.stations.length)
const warningCount = computed(() => props.warnings.length)
const activeWarningCount = computed(() => props.controlState.activeWarnings.size)

// FPS监控
let frameCount = 0
let lastTime = performance.now()
let fpsInterval: number | null = null

const updateFPS = () => {
  frameCount++
  const currentTime = performance.now()
  
  if (currentTime - lastTime >= 1000) {
    fps.value = Math.round(frameCount * 1000 / (currentTime - lastTime))
    frameCount = 0
    lastTime = currentTime
  }
  
  requestAnimationFrame(updateFPS)
}

// 更新时间
const updateTime = () => {
  lastUpdateTime.value = new Date().toLocaleTimeString('zh-CN')
}

// 生命周期
onMounted(() => {
  // 开始FPS监控
  updateFPS()
  
  // 定时更新时间
  fpsInterval = window.setInterval(updateTime, 1000)
  updateTime()
  
  // 检查是否是开发环境，自动显示状态
  if (import.meta.env.DEV) {
    setTimeout(() => {
      showStatus.value = true
    }, 2000)
  }
})

onBeforeUnmount(() => {
  if (fpsInterval) {
    clearInterval(fpsInterval)
  }
})

// 暴露方法给父组件
defineExpose({
  setSceneStatus: (status: boolean) => {
    sceneStatus.value = status
  },
  setDataUpdateStatus: (status: boolean) => {
    dataUpdateStatus.value = status
  }
})
</script>

<style scoped>
/* 动画效果 */
.transition-colors {
  transition: color 0.2s ease, background-color 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .max-w-sm {
    max-width: calc(100vw - 2rem);
  }
}
</style>
