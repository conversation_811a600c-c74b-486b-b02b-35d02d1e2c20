<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-900 to-purple-900 text-white p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-4xl font-bold mb-8 text-center">🌍 珠海市气象预警系统</h1>
      
      <!-- 状态卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
          <div class="text-2xl mb-2">🌧️</div>
          <h3 class="text-lg font-semibold mb-2">暴雨预警</h3>
          <p class="text-sm opacity-80">当前无活跃预警</p>
        </div>
        
        <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
          <div class="text-2xl mb-2">💨</div>
          <h3 class="text-lg font-semibold mb-2">风力预警</h3>
          <p class="text-sm opacity-80">监测中...</p>
        </div>
        
        <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
          <div class="text-2xl mb-2">📍</div>
          <h3 class="text-lg font-semibold mb-2">监测站点</h3>
          <p class="text-sm opacity-80">{{ stationCount }} 个站点在线</p>
        </div>
      </div>
      
      <!-- 控制按钮 -->
      <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">系统控制</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button 
            @click="toggleRainWarning"
            :class="[
              'p-4 rounded-lg transition-all duration-200',
              rainWarning ? 'bg-blue-600 shadow-lg' : 'bg-gray-600 hover:bg-gray-500'
            ]"
          >
            <div class="text-2xl mb-2">🌧️</div>
            <div class="text-sm">暴雨预警</div>
          </button>
          
          <button 
            @click="toggleWindWarning"
            :class="[
              'p-4 rounded-lg transition-all duration-200',
              windWarning ? 'bg-yellow-600 shadow-lg' : 'bg-gray-600 hover:bg-gray-500'
            ]"
          >
            <div class="text-2xl mb-2">💨</div>
            <div class="text-sm">风力预警</div>
          </button>
          
          <button 
            @click="refreshData"
            class="p-4 rounded-lg bg-green-600 hover:bg-green-700 transition-all duration-200"
          >
            <div class="text-2xl mb-2">🔄</div>
            <div class="text-sm">刷新数据</div>
          </button>
          
          <button 
            @click="showInfo"
            class="p-4 rounded-lg bg-purple-600 hover:bg-purple-700 transition-all duration-200"
          >
            <div class="text-2xl mb-2">ℹ️</div>
            <div class="text-sm">系统信息</div>
          </button>
        </div>
      </div>
      
      <!-- 珠海市信息 -->
      <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">📍 珠海市概况</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <h3 class="font-semibold text-green-400">香洲区</h3>
            <p class="text-sm opacity-80">主城区，包含拱北、吉大等地</p>
          </div>
          <div>
            <h3 class="font-semibold text-blue-400">金湾区</h3>
            <p class="text-sm opacity-80">包含三灶、红旗、平沙等地</p>
          </div>
          <div>
            <h3 class="font-semibold text-purple-400">斗门区</h3>
            <p class="text-sm opacity-80">包含井岸、白蕉、斗门等地</p>
          </div>
        </div>
      </div>
      
      <!-- 日志显示 -->
      <div class="bg-black/30 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-3">📋 系统日志</h3>
        <div class="space-y-1 max-h-40 overflow-y-auto">
          <div v-for="(log, index) in logs" :key="index" class="text-sm">
            <span class="text-gray-400">[{{ log.time }}]</span>
            <span :class="getLogColor(log.type)">{{ log.message }}</span>
          </div>
        </div>
      </div>
      
      <!-- 底部信息 -->
      <div class="text-center mt-8 text-sm opacity-60">
        <p>珠海市三维气象预警系统 v2.0</p>
        <p>基于 Vue 3 + TypeScript + Cesium.js</p>
        <p>当前时间: {{ currentTime }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'

// 响应式数据
const stationCount = ref(10)
const rainWarning = ref(false)
const windWarning = ref(false)
const currentTime = ref('')
const logs = ref<Array<{type: string, message: string, time: string}>>([])

// 方法
const toggleRainWarning = () => {
  rainWarning.value = !rainWarning.value
  addLog('info', `暴雨预警已${rainWarning.value ? '开启' : '关闭'}`)
}

const toggleWindWarning = () => {
  windWarning.value = !windWarning.value
  addLog('info', `风力预警已${windWarning.value ? '开启' : '关闭'}`)
}

const refreshData = () => {
  addLog('success', '正在刷新气象数据...')
  setTimeout(() => {
    addLog('success', '数据刷新完成')
  }, 1000)
}

const showInfo = () => {
  addLog('info', '系统运行正常，所有功能可用')
}

const addLog = (type: string, message: string) => {
  logs.value.unshift({
    type,
    message,
    time: new Date().toLocaleTimeString()
  })
  
  // 保持最多20条日志
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20)
  }
}

const getLogColor = (type: string) => {
  switch (type) {
    case 'success': return 'text-green-400'
    case 'warning': return 'text-yellow-400'
    case 'error': return 'text-red-400'
    default: return 'text-blue-400'
  }
}

const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

// 生命周期
let timeInterval: number | null = null

onMounted(() => {
  addLog('success', '系统初始化完成')
  addLog('info', '珠海市气象预警系统已启动')
  
  updateTime()
  timeInterval = window.setInterval(updateTime, 1000)
})

onBeforeUnmount(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
/* 动画效果 */
.transition-all {
  transition: all 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
</style>
