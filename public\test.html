<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>珠海市气象预警系统 - 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3a8a 0%, #7c3aed 100%);
            color: white;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .title {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.8;
        }
        
        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .card-text {
            opacity: 0.8;
            margin-bottom: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }
        
        .btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
        
        .btn-primary {
            background: #3b82f6;
        }
        
        .btn-success {
            background: #10b981;
        }
        
        .btn-warning {
            background: #f59e0b;
        }
        
        .btn-danger {
            background: #ef4444;
        }
        
        .info-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .districts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .district {
            text-align: center;
            padding: 1rem;
        }
        
        .district-title {
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
        }
        
        .district-desc {
            opacity: 0.8;
            font-size: 0.9rem;
        }
        
        .log-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 1rem;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .log-container {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 0.5rem;
            padding: 1rem;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .footer {
            text-align: center;
            margin-top: 3rem;
            opacity: 0.6;
            font-size: 0.9rem;
        }
        
        .status {
            display: inline-block;
            padding: 0.2rem 0.5rem;
            border-radius: 0.3rem;
            font-size: 0.8rem;
            margin-left: 0.5rem;
        }
        
        .status-active {
            background: #10b981;
        }
        
        .status-inactive {
            background: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 标题 -->
        <div class="header">
            <h1 class="title">🌍 珠海市气象预警系统</h1>
            <p class="subtitle">基于Vue3 + TypeScript的现代化气象监测平台</p>
        </div>

        <!-- 状态卡片 -->
        <div class="cards">
            <div class="card">
                <div class="card-icon">🌧️</div>
                <h3 class="card-title">暴雨预警</h3>
                <p class="card-text">当前状态: <span id="rain-status" class="status status-inactive">未激活</span></p>
                <button class="btn btn-primary" onclick="toggleRain()">切换状态</button>
            </div>

            <div class="card">
                <div class="card-icon">💨</div>
                <h3 class="card-title">风力预警</h3>
                <p class="card-text">当前状态: <span id="wind-status" class="status status-inactive">未激活</span></p>
                <button class="btn btn-warning" onclick="toggleWind()">切换状态</button>
            </div>

            <div class="card">
                <div class="card-icon">📍</div>
                <h3 class="card-title">监测站点</h3>
                <p class="card-text">在线站点: <span id="station-count">10</span> 个</p>
                <button class="btn btn-success" onclick="refreshStations()">刷新数据</button>
            </div>
        </div>

        <!-- 珠海市区域信息 -->
        <div class="info-section">
            <h2 style="font-size: 1.8rem; font-weight: 600; margin-bottom: 2rem; text-align: center;">📍 珠海市行政区划</h2>
            <div class="districts">
                <div class="district">
                    <h3 class="district-title" style="color: #10b981;">香洲区</h3>
                    <p class="district-desc">主城区，包含拱北口岸、吉大、前山等地</p>
                    <div style="margin-top: 0.5rem; font-size: 0.8rem; opacity: 0.7;">监测站点: 4个</div>
                </div>
                <div class="district">
                    <h3 class="district-title" style="color: #3b82f6;">金湾区</h3>
                    <p class="district-desc">西部区域，包含三灶、红旗、平沙等地</p>
                    <div style="margin-top: 0.5rem; font-size: 0.8rem; opacity: 0.7;">监测站点: 3个</div>
                </div>
                <div class="district">
                    <h3 class="district-title" style="color: #8b5cf6;">斗门区</h3>
                    <p class="district-desc">北部区域，包含井岸、白蕉、斗门等地</p>
                    <div style="margin-top: 0.5rem; font-size: 0.8rem; opacity: 0.7;">监测站点: 3个</div>
                </div>
            </div>
        </div>

        <!-- 系统日志 -->
        <div class="log-section">
            <h3 style="font-size: 1.3rem; font-weight: 600; margin-bottom: 1rem;">📋 系统日志</h3>
            <div class="log-container" id="log-container">
                <div class="log-entry">
                    <span style="color: #6b7280;">[系统启动]</span>
                    <span style="color: #10b981;">珠海市气象预警系统测试页面加载成功</span>
                </div>
            </div>
        </div>

        <!-- 底部信息 -->
        <div class="footer">
            <p>珠海市三维气象预警系统 v2.0</p>
            <p>基于 Vue 3 + TypeScript</p>
            <p>当前时间: <span id="current-time"></span></p>
        </div>
    </div>

    <script>
        let rainActive = false;
        let windActive = false;
        let stationCount = 10;

        function toggleRain() {
            rainActive = !rainActive;
            const status = document.getElementById('rain-status');
            status.textContent = rainActive ? '已激活' : '未激活';
            status.className = `status ${rainActive ? 'status-active' : 'status-inactive'}`;
            addLog('info', `暴雨预警已${rainActive ? '开启' : '关闭'}`);
        }

        function toggleWind() {
            windActive = !windActive;
            const status = document.getElementById('wind-status');
            status.textContent = windActive ? '已激活' : '未激活';
            status.className = `status ${windActive ? 'status-active' : 'status-inactive'}`;
            addLog('info', `风力预警已${windActive ? '开启' : '关闭'}`);
        }

        function refreshStations() {
            addLog('success', '正在刷新监测站点数据...');
            setTimeout(() => {
                stationCount = Math.floor(Math.random() * 3) + 8; // 8-10个站点
                document.getElementById('station-count').textContent = stationCount;
                addLog('success', `数据刷新完成，当前在线站点: ${stationCount}个`);
            }, 1000);
        }

        function addLog(type, message) {
            const container = document.getElementById('log-container');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            
            const time = new Date().toLocaleTimeString();
            const color = type === 'success' ? '#10b981' : type === 'warning' ? '#f59e0b' : '#3b82f6';
            
            entry.innerHTML = `
                <span style="color: #6b7280;">[${time}]</span>
                <span style="color: ${color};">${message}</span>
            `;
            
            container.insertBefore(entry, container.firstChild);
            
            // 保持最多10条日志
            while (container.children.length > 10) {
                container.removeChild(container.lastChild);
            }
        }

        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');
        }

        // 初始化
        updateTime();
        setInterval(updateTime, 1000);
        
        // 添加初始日志
        setTimeout(() => {
            addLog('success', '所有监测站点连接正常');
        }, 1000);
    </script>
</body>
</html>
