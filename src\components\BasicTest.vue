<template>
  <div style="min-height: 100vh; background: linear-gradient(135deg, #1e3a8a 0%, #7c3aed 100%); color: white; padding: 2rem;">
    <div style="max-width: 1200px; margin: 0 auto;">
      <!-- 标题 -->
      <div style="text-align: center; margin-bottom: 3rem;">
        <h1 style="font-size: 3rem; font-weight: bold; margin-bottom: 1rem;">
          🌍 珠海市气象预警系统
        </h1>
        <p style="font-size: 1.2rem; opacity: 0.8;">
          基于Vue3 + TypeScript的现代化气象监测平台
        </p>
      </div>

      <!-- 状态卡片 -->
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-bottom: 3rem;">
        <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 1rem; padding: 2rem; border: 1px solid rgba(255,255,255,0.2);">
          <div style="font-size: 3rem; margin-bottom: 1rem;">🌧️</div>
          <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 0.5rem;">暴雨预警</h3>
          <p style="opacity: 0.8;">当前状态: {{ rainWarning ? '已激活' : '未激活' }}</p>
          <button 
            @click="toggleRain"
            :style="{
              marginTop: '1rem',
              padding: '0.5rem 1rem',
              borderRadius: '0.5rem',
              border: 'none',
              background: rainWarning ? '#ef4444' : '#6b7280',
              color: 'white',
              cursor: 'pointer',
              fontSize: '0.9rem'
            }"
          >
            {{ rainWarning ? '关闭预警' : '开启预警' }}
          </button>
        </div>

        <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 1rem; padding: 2rem; border: 1px solid rgba(255,255,255,0.2);">
          <div style="font-size: 3rem; margin-bottom: 1rem;">💨</div>
          <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 0.5rem;">风力预警</h3>
          <p style="opacity: 0.8;">当前状态: {{ windWarning ? '已激活' : '未激活' }}</p>
          <button 
            @click="toggleWind"
            :style="{
              marginTop: '1rem',
              padding: '0.5rem 1rem',
              borderRadius: '0.5rem',
              border: 'none',
              background: windWarning ? '#f59e0b' : '#6b7280',
              color: 'white',
              cursor: 'pointer',
              fontSize: '0.9rem'
            }"
          >
            {{ windWarning ? '关闭预警' : '开启预警' }}
          </button>
        </div>

        <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 1rem; padding: 2rem; border: 1px solid rgba(255,255,255,0.2);">
          <div style="font-size: 3rem; margin-bottom: 1rem;">📍</div>
          <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 0.5rem;">监测站点</h3>
          <p style="opacity: 0.8;">在线站点: {{ stationCount }} 个</p>
          <button 
            @click="refreshStations"
            style="margin-top: 1rem; padding: 0.5rem 1rem; border-radius: 0.5rem; border: none; background: #10b981; color: white; cursor: pointer; font-size: 0.9rem;"
          >
            刷新数据
          </button>
        </div>
      </div>

      <!-- 珠海市区域信息 -->
      <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 1rem; padding: 2rem; margin-bottom: 3rem; border: 1px solid rgba(255,255,255,0.2);">
        <h2 style="font-size: 1.8rem; font-weight: 600; margin-bottom: 2rem; text-align: center;">📍 珠海市行政区划</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
          <div style="text-align: center; padding: 1rem;">
            <h3 style="color: #10b981; font-size: 1.3rem; margin-bottom: 0.5rem;">香洲区</h3>
            <p style="opacity: 0.8; font-size: 0.9rem;">主城区，包含拱北口岸、吉大、前山等地</p>
            <div style="margin-top: 0.5rem; font-size: 0.8rem; opacity: 0.7;">监测站点: 4个</div>
          </div>
          <div style="text-align: center; padding: 1rem;">
            <h3 style="color: #3b82f6; font-size: 1.3rem; margin-bottom: 0.5rem;">金湾区</h3>
            <p style="opacity: 0.8; font-size: 0.9rem;">西部区域，包含三灶、红旗、平沙等地</p>
            <div style="margin-top: 0.5rem; font-size: 0.8rem; opacity: 0.7;">监测站点: 3个</div>
          </div>
          <div style="text-align: center; padding: 1rem;">
            <h3 style="color: #8b5cf6; font-size: 1.3rem; margin-bottom: 0.5rem;">斗门区</h3>
            <p style="opacity: 0.8; font-size: 0.9rem;">北部区域，包含井岸、白蕉、斗门等地</p>
            <div style="margin-top: 0.5rem; font-size: 0.8rem; opacity: 0.7;">监测站点: 3个</div>
          </div>
        </div>
      </div>

      <!-- 系统日志 -->
      <div style="background: rgba(0,0,0,0.3); border-radius: 1rem; padding: 2rem; border: 1px solid rgba(255,255,255,0.1);">
        <h3 style="font-size: 1.3rem; font-weight: 600; margin-bottom: 1rem;">📋 系统日志</h3>
        <div style="background: rgba(0,0,0,0.5); border-radius: 0.5rem; padding: 1rem; max-height: 200px; overflow-y: auto;">
          <div v-for="(log, index) in logs" :key="index" style="margin-bottom: 0.5rem; font-size: 0.9rem;">
            <span style="color: #6b7280;">[{{ log.time }}]</span>
            <span :style="{ color: getLogColor(log.type) }">{{ log.message }}</span>
          </div>
          <div v-if="logs.length === 0" style="color: #6b7280; font-style: italic;">
            暂无日志记录...
          </div>
        </div>
      </div>

      <!-- 底部信息 -->
      <div style="text-align: center; margin-top: 3rem; opacity: 0.6; font-size: 0.9rem;">
        <p>珠海市三维气象预警系统 v2.0</p>
        <p>基于 Vue 3 + TypeScript</p>
        <p>当前时间: {{ currentTime }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'

// 响应式数据
const rainWarning = ref(false)
const windWarning = ref(false)
const stationCount = ref(10)
const currentTime = ref('')
const logs = ref<Array<{type: string, message: string, time: string}>>([])

// 方法
const toggleRain = () => {
  rainWarning.value = !rainWarning.value
  addLog('info', `暴雨预警已${rainWarning.value ? '开启' : '关闭'}`)
}

const toggleWind = () => {
  windWarning.value = !windWarning.value
  addLog('info', `风力预警已${windWarning.value ? '开启' : '关闭'}`)
}

const refreshStations = () => {
  addLog('success', '正在刷新监测站点数据...')
  setTimeout(() => {
    stationCount.value = Math.floor(Math.random() * 3) + 8 // 8-10个站点
    addLog('success', `数据刷新完成，当前在线站点: ${stationCount.value}个`)
  }, 1000)
}

const addLog = (type: string, message: string) => {
  logs.value.unshift({
    type,
    message,
    time: new Date().toLocaleTimeString()
  })
  
  // 保持最多10条日志
  if (logs.value.length > 10) {
    logs.value = logs.value.slice(0, 10)
  }
}

const getLogColor = (type: string) => {
  switch (type) {
    case 'success': return '#10b981'
    case 'warning': return '#f59e0b'
    case 'error': return '#ef4444'
    default: return '#3b82f6'
  }
}

const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

// 生命周期
let timeInterval: number | null = null

onMounted(() => {
  addLog('success', '珠海市气象预警系统启动成功')
  addLog('info', '正在初始化监测站点...')
  
  setTimeout(() => {
    addLog('success', '所有监测站点连接正常')
  }, 1500)
  
  updateTime()
  timeInterval = window.setInterval(updateTime, 1000)
})

onBeforeUnmount(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
/* 确保按钮有悬停效果 */
button:hover {
  opacity: 0.8;
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
