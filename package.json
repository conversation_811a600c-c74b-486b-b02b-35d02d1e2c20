{"name": "<PERSON>iang<PERSON>kai<PERSON>", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@tailwindcss/postcss": "^4.1.11", "@tweenjs/tween.js": "^25.0.0", "@types/three": "^0.178.0", "autoprefixer": "^10.4.21", "cesium": "^1.131.0", "gsap": "^3.13.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "three": "^0.178.0", "vue": "^3.5.17"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^8.0.4", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-static-copy": "^3.1.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}