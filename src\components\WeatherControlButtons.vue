<template>
  <div class="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
    <!-- 主控制面板 -->
    <div 
      class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl p-4 transition-all duration-300"
      :class="{ 'scale-95': !isExpanded }"
    >
      <!-- 展开/收起按钮 -->
      <div class="flex justify-center mb-4">
        <button
          @click="toggleExpanded"
          class="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
        >
          <span class="text-sm font-medium">气象监测控制台</span>
          <svg 
            class="w-4 h-4 transition-transform duration-200"
            :class="{ 'rotate-180': isExpanded }"
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>
      
      <!-- 控制按钮组 -->
      <div 
        v-show="isExpanded"
        class="grid grid-cols-2 md:grid-cols-4 gap-3 transition-all duration-300"
      >
        <!-- 预警类型控制 -->
        <div 
          v-for="warningType in warningTypes" 
          :key="warningType.type"
          class="relative"
        >
          <button
            @click="toggleWarning(warningType.type)"
            :class="[
              'w-full p-3 rounded-xl text-white font-medium text-sm transition-all duration-200 transform hover:scale-105',
              isWarningActive(warningType.type) 
                ? 'shadow-lg ring-2 ring-white/50' 
                : 'opacity-60 hover:opacity-80'
            ]"
            :style="{ backgroundColor: warningType.color }"
          >
            <div class="flex flex-col items-center space-y-1">
              <span class="text-lg">{{ warningType.icon }}</span>
              <span>{{ warningType.name }}</span>
            </div>
            
            <!-- 活跃指示器 -->
            <div 
              v-if="isWarningActive(warningType.type)"
              class="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse"
            ></div>
          </button>
        </div>
        
        <!-- 视图控制按钮 -->
        <button
          @click="resetView"
          class="p-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors"
        >
          <div class="flex flex-col items-center space-y-1">
            <span class="text-lg">🏠</span>
            <span class="text-sm">重置视图</span>
          </div>
        </button>
        
        <button
          @click="toggleStations"
          :class="[
            'p-3 rounded-xl transition-colors',
            showStations 
              ? 'bg-green-600 text-white hover:bg-green-700' 
              : 'bg-gray-400 text-white hover:bg-gray-500'
          ]"
        >
          <div class="flex flex-col items-center space-y-1">
            <span class="text-lg">📍</span>
            <span class="text-sm">监测站</span>
          </div>
        </button>
        
        <button
          @click="toggleWarningAreas"
          :class="[
            'p-3 rounded-xl transition-colors',
            showWarningAreas 
              ? 'bg-orange-600 text-white hover:bg-orange-700' 
              : 'bg-gray-400 text-white hover:bg-gray-500'
          ]"
        >
          <div class="flex flex-col items-center space-y-1">
            <span class="text-lg">⚠️</span>
            <span class="text-sm">预警区域</span>
          </div>
        </button>
        
        <button
          @click="openFullControl"
          class="p-3 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-colors"
        >
          <div class="flex flex-col items-center space-y-1">
            <span class="text-lg">⚙️</span>
            <span class="text-sm">详细设置</span>
          </div>
        </button>
      </div>
      
      <!-- 快速信息显示 -->
      <div 
        v-show="isExpanded"
        class="mt-4 pt-4 border-t border-gray-200"
      >
        <div class="grid grid-cols-3 gap-4 text-center">
          <div class="bg-blue-50 rounded-lg p-2">
            <div class="text-lg font-bold text-blue-600">{{ activeWarningsCount }}</div>
            <div class="text-xs text-gray-600">活跃预警</div>
          </div>
          <div class="bg-green-50 rounded-lg p-2">
            <div class="text-lg font-bold text-green-600">{{ totalStations }}</div>
            <div class="text-xs text-gray-600">监测站点</div>
          </div>
          <div class="bg-orange-50 rounded-lg p-2">
            <div class="text-lg font-bold text-orange-600">{{ currentTime }}</div>
            <div class="text-xs text-gray-600">当前时间</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 浮动通知 -->
    <div 
      v-if="notification"
      class="absolute bottom-full mb-4 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-4 py-2 rounded-lg text-sm whitespace-nowrap"
    >
      {{ notification }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { WeatherWarningType } from '@/types/weather'
import type {
  WeatherWarningArea,
  MonitoringStation,
  ControlPanelState
} from '@/types/weather'

// Props
interface Props {
  warnings: WeatherWarningArea[]
  stations: MonitoringStation[]
  controlState: ControlPanelState
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  controlStateUpdate: [state: ControlPanelState]
  resetView: []
  openFullControl: []
}>()

// 响应式数据
const isExpanded = ref(false)
const notification = ref('')
const currentTime = ref('')

// 预警类型配置
const warningTypes = [
  {
    type: WeatherWarningType.HEAVY_RAIN,
    name: '暴雨',
    icon: '🌧️',
    color: '#1e40af'
  },
  {
    type: WeatherWarningType.STRONG_WIND,
    name: '狂风',
    icon: '💨',
    color: '#f59e0b'
  },
  {
    type: WeatherWarningType.GALE,
    name: '强风',
    icon: '🌪️',
    color: '#dc2626'
  },
  {
    type: WeatherWarningType.TORRENTIAL_RAIN,
    name: '强降雨',
    icon: '⛈️',
    color: '#1e3a8a'
  }
]

// 计算属性
const activeWarningsCount = computed(() => {
  return props.warnings.filter(warning => 
    props.controlState.activeWarnings.has(warning.type)
  ).length
})

const totalStations = computed(() => props.stations.length)

const showStations = computed(() => props.controlState.showStations)
const showWarningAreas = computed(() => props.controlState.showWarningAreas)

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const toggleWarning = (type: WeatherWarningType) => {
  const newActiveWarnings = new Set(props.controlState.activeWarnings)
  
  if (newActiveWarnings.has(type)) {
    newActiveWarnings.delete(type)
    showNotification(`已关闭${getWarningTypeName(type)}`)
  } else {
    newActiveWarnings.add(type)
    showNotification(`已开启${getWarningTypeName(type)}`)
  }
  
  updateControlState({ activeWarnings: newActiveWarnings })
}

const isWarningActive = (type: WeatherWarningType) => {
  return props.controlState.activeWarnings.has(type)
}

const toggleStations = () => {
  const newValue = !props.controlState.showStations
  updateControlState({ showStations: newValue })
  showNotification(newValue ? '已显示监测站点' : '已隐藏监测站点')
}

const toggleWarningAreas = () => {
  const newValue = !props.controlState.showWarningAreas
  updateControlState({ showWarningAreas: newValue })
  showNotification(newValue ? '已显示预警区域' : '已隐藏预警区域')
}

const resetView = () => {
  emit('resetView')
  showNotification('视图已重置')
}

const openFullControl = () => {
  emit('openFullControl')
}

const updateControlState = (updates: Partial<ControlPanelState>) => {
  const newState = { ...props.controlState, ...updates }
  emit('controlStateUpdate', newState)
}

const showNotification = (message: string) => {
  notification.value = message
  setTimeout(() => {
    notification.value = ''
  }, 2000)
}

const getWarningTypeName = (type: WeatherWarningType): string => {
  const typeConfig = warningTypes.find(t => t.type === type)
  return typeConfig ? typeConfig.name : '未知'
}

const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// 生命周期
let timeInterval: number | null = null

onMounted(() => {
  updateTime()
  timeInterval = window.setInterval(updateTime, 1000)
  
  // 自动展开（首次访问）
  setTimeout(() => {
    isExpanded.value = true
  }, 1000)
})

onBeforeUnmount(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 640px) {
  .fixed {
    left: 1rem;
    right: 1rem;
    transform: none;
  }
  
  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

/* 动画效果 */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style>
