<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { gsap } from 'gsap'

const container = ref<HTMLDivElement | null>(null)
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let controls: OrbitControls
let raindrops: THREE.Points[] = []
let windParticles: THREE.Points[] = []
let fogParticles: THREE.Points[] = []
let clock: THREE.Clock

// 性能监控
let stats: any
let frameId: number

// 初始化场景
const initScene = () => {
  clock = new THREE.Clock()
  scene = new THREE.Scene()
  scene.fog = new THREE.Fog(0x87ceeb, 1, 1000)
  
  camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000)
  renderer = new THREE.WebGLRenderer({ 
    antialias: true,
    alpha: true
  })
  renderer.setSize(window.innerWidth, window.innerHeight)
  renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFSoftShadowMap
  container.value?.appendChild(renderer.domElement)

  // 添加环境光和平行光
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.5)
  scene.add(ambientLight)
  
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight.position.set(10, 10, 10)
  directionalLight.castShadow = true
  directionalLight.shadow.mapSize.width = 2048
  directionalLight.shadow.mapSize.height = 2048
  scene.add(directionalLight)

  // 添加地形
  const terrainGeometry = new THREE.PlaneGeometry(200, 200, 100, 100)
  const terrainMaterial = new THREE.MeshPhongMaterial({
    color: 0x3a5f0b,
    wireframe: false,
  })
  
  // 添加地形起伏
  const vertices = terrainGeometry.attributes.position.array as Float32Array
  for (let i = 0; i < vertices.length; i += 3) {
    vertices[i + 1] = Math.sin(vertices[i] / 10) * Math.cos(vertices[i + 2] / 10) * 5
  }
  terrainGeometry.computeVertexNormals()
  
  const terrain = new THREE.Mesh(terrainGeometry, terrainMaterial)
  terrain.rotation.x = -Math.PI / 2
  terrain.receiveShadow = true
  scene.add(terrain)

  // 添加天空盒
  const skyGeometry = new THREE.BoxGeometry(1000, 1000, 1000)
  const skyMaterials = [
    new THREE.MeshBasicMaterial({ 
      map: new THREE.TextureLoader().load('/textures/skybox/right.jpg'),
      side: THREE.BackSide 
    }),
    new THREE.MeshBasicMaterial({ 
      map: new THREE.TextureLoader().load('/textures/skybox/left.jpg'),
      side: THREE.BackSide 
    }),
    new THREE.MeshBasicMaterial({ 
      map: new THREE.TextureLoader().load('/textures/skybox/top.jpg'),
      side: THREE.BackSide 
    }),
    new THREE.MeshBasicMaterial({ 
      map: new THREE.TextureLoader().load('/textures/skybox/bottom.jpg'),
      side: THREE.BackSide 
    }),
    new THREE.MeshBasicMaterial({ 
      map: new THREE.TextureLoader().load('/textures/skybox/front.jpg'),
      side: THREE.BackSide 
    }),
    new THREE.MeshBasicMaterial({ 
      map: new THREE.TextureLoader().load('/textures/skybox/back.jpg'),
      side: THREE.BackSide 
    }),
  ]
  const sky = new THREE.Mesh(skyGeometry, skyMaterials)
  scene.add(sky)

  // 设置相机位置
  camera.position.set(0, 30, 100)
  camera.lookAt(0, 0, 0)

  // 添加控制器
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true
  controls.dampingFactor = 0.05
  controls.maxPolarAngle = Math.PI / 2 - 0.1 // 限制相机俯仰角度
}

// 创建雨滴效果
const createRain = () => {
  const rainGeometry = new THREE.BufferGeometry()
  const rainVertices = []
  const rainVelocities = []
  
  for (let i = 0; i < 3000; i++) {
    const x = Math.random() * 200 - 100
    const y = Math.random() * 100
    const z = Math.random() * 200 - 100
    rainVertices.push(x, y, z)
    rainVelocities.push(
      Math.random() * 0.1 - 0.05,  // x velocity
      -Math.random() * 0.5 - 0.5,  // y velocity (falling)
      Math.random() * 0.1 - 0.05   // z velocity
    )
  }
  
  rainGeometry.setAttribute('position', new THREE.Float32BufferAttribute(rainVertices, 3))
  const rainMaterial = new THREE.PointsMaterial({
    color: 0x33b5e5,
    size: 0.1,
    transparent: true,
    opacity: 0.6,
    blending: THREE.AdditiveBlending
  })
  
  const rain = new THREE.Points(rainGeometry, rainMaterial) as any
  rain.velocities = rainVelocities
  scene.add(rain)
  raindrops.push(rain)
}

// 创建风效果
const createWind = () => {
  const windGeometry = new THREE.BufferGeometry()
  const windVertices = []
  const windVelocities = []
  
  for (let i = 0; i < 1000; i++) {
    const x = Math.random() * 200 - 100
    const y = Math.random() * 50
    const z = Math.random() * 200 - 100
    windVertices.push(x, y, z)
    windVelocities.push(
      Math.random() * 0.3 + 0.2,  // x velocity (主要风向)
      Math.random() * 0.1 - 0.05, // y velocity
      Math.random() * 0.2 - 0.1   // z velocity
    )
  }
  
  windGeometry.setAttribute('position', new THREE.Float32BufferAttribute(windVertices, 3))
  const windMaterial = new THREE.PointsMaterial({
    color: 0xffbb33,
    size: 0.2,
    transparent: true,
    opacity: 0.4,
    blending: THREE.AdditiveBlending
  })
  
  const wind = new THREE.Points(windGeometry, windMaterial) as any
  wind.velocities = windVelocities
  scene.add(wind)
  windParticles.push(wind)
}

// 创建雾效果
const createFog = () => {
  const fogGeometry = new THREE.BufferGeometry()
  const fogVertices = []
  const fogVelocities = []
  
  for (let i = 0; i < 500; i++) {
    const x = Math.random() * 200 - 100
    const y = Math.random() * 20
    const z = Math.random() * 200 - 100
    fogVertices.push(x, y, z)
    fogVelocities.push(
      Math.random() * 0.05 - 0.025,
      Math.random() * 0.02 - 0.01,
      Math.random() * 0.05 - 0.025
    )
  }
  
  fogGeometry.setAttribute('position', new THREE.Float32BufferAttribute(fogVertices, 3))
  const fogMaterial = new THREE.PointsMaterial({
    color: 0xcccccc,
    size: 0.5,
    transparent: true,
    opacity: 0.3,
    blending: THREE.AdditiveBlending
  })
  
  const fog = new THREE.Points(fogGeometry, fogMaterial) as any
  fog.velocities = fogVelocities
  scene.add(fog)
  fogParticles.push(fog)
}

// 更新粒子位置
const updateParticles = () => {
  const delta = clock.getDelta()
  
  // 更新雨滴
  raindrops.forEach(raindrop => {
    const positions = raindrop.geometry.attributes.position.array as Float32Array
    for (let i = 0; i < positions.length; i += 3) {
      const vIndex = i / 3
      positions[i] += raindrop.velocities[vIndex * 3] // x
      positions[i + 1] += raindrop.velocities[vIndex * 3 + 1] // y
      positions[i + 2] += raindrop.velocities[vIndex * 3 + 2] // z
      
      // 重置位置
      if (positions[i + 1] < 0) {
        positions[i + 1] = 100
        positions[i] = Math.random() * 200 - 100
        positions[i + 2] = Math.random() * 200 - 100
      }
    }
    raindrop.geometry.attributes.position.needsUpdate = true
  })
  
  // 更新风粒子
  windParticles.forEach(particle => {
    const positions = particle.geometry.attributes.position.array as Float32Array
    for (let i = 0; i < positions.length; i += 3) {
      const vIndex = i / 3
      positions[i] += particle.velocities[vIndex * 3] * delta * 60
      positions[i + 1] += particle.velocities[vIndex * 3 + 1] * delta * 60
      positions[i + 2] += particle.velocities[vIndex * 3 + 2] * delta * 60
      
      // 重置位置
      if (positions[i] > 100) positions[i] = -100
      if (positions[i + 1] > 50) positions[i + 1] = 0
      if (positions[i + 2] > 100) positions[i + 2] = -100
    }
    particle.geometry.attributes.position.needsUpdate = true
  })
  
  // 更新雾粒子
  fogParticles.forEach(particle => {
    const positions = particle.geometry.attributes.position.array as Float32Array
    for (let i = 0; i < positions.length; i += 3) {
      const vIndex = i / 3
      positions[i] += particle.velocities[vIndex * 3] * delta * 30
      positions[i + 1] += particle.velocities[vIndex * 3 + 1] * delta * 30
      positions[i + 2] += particle.velocities[vIndex * 3 + 2] * delta * 30
      
      // 保持在场景范围内
      if (Math.abs(positions[i]) > 100) particle.velocities[vIndex * 3] *= -1
      if (Math.abs(positions[i + 1]) > 20) particle.velocities[vIndex * 3 + 1] *= -1
      if (Math.abs(positions[i + 2]) > 100) particle.velocities[vIndex * 3 + 2] *= -1
    }
    particle.geometry.attributes.position.needsUpdate = true
  })
}

// 动画循环
const animate = () => {
  frameId = requestAnimationFrame(animate)
  
  updateParticles()
  controls.update()
  renderer.render(scene, camera)
}

// 清理场景
const cleanupScene = () => {
  cancelAnimationFrame(frameId)
  
  // 清理几何体和材质
  scene.traverse((object) => {
    if (object instanceof THREE.Mesh) {
      object.geometry.dispose()
      if (Array.isArray(object.material)) {
        object.material.forEach(material => material.dispose())
      } else {
        object.material.dispose()
      }
    }
  })
  
  // 清理渲染器
  renderer.dispose()
  
  // 清理控制器
  controls.dispose()
}

// 窗口大小调整
const handleResize = () => {
  camera.aspect = window.innerWidth / window.innerHeight
  camera.updateProjectionMatrix()
  renderer.setSize(window.innerWidth, window.innerHeight)
  renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
}

// 生命周期钩子
onMounted(() => {
  initScene()
  animate()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  cleanupScene()
})

// 暴露给父组件的方法
defineExpose({
  createRain,
  createWind,
  createFog
})
</script>

<template>
  <div ref="container" class="w-full h-full"></div>
</template>

<style scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style> 