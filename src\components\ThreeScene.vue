<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { gsap } from 'gsap'
import { WeatherWarningType } from '@/types/weather'
import type {
  WeatherWarningArea,
  MonitoringStation,
  Position3D,
  ControlPanelState
} from '@/types/weather'
import { defaultWeatherEffects, defaultSceneConfig, stationIconConfig } from '@/config/weatherConfig'

// Props
interface Props {
  warnings: WeatherWarningArea[]
  stations: MonitoringStation[]
  controlState: ControlPanelState
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  stationClick: [station: MonitoringStation]
  warningAreaClick: [warning: WeatherWarningArea]
}>()

const container = ref<HTMLDivElement | null>(null)
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let controls: OrbitControls
let clock: THREE.Clock
let frameId: number

// 气象效果对象
let weatherEffects: Map<string, THREE.Group> = new Map()
let stationMeshes: Map<string, THREE.Mesh> = new Map()
let warningAreaMeshes: Map<string, THREE.Mesh> = new Map()

// 粒子系统
let particleSystems: Map<WeatherWarningType, THREE.Points[]> = new Map()

// 鼠标交互
let raycaster: THREE.Raycaster
let mouse: THREE.Vector2

// 初始化场景
const initScene = () => {
  clock = new THREE.Clock()
  scene = new THREE.Scene()

  // 使用配置文件中的雾效设置
  const fogColor = parseInt(defaultSceneConfig.fog.color.replace('#', '0x'))
  scene.fog = new THREE.Fog(fogColor, defaultSceneConfig.fog.near, defaultSceneConfig.fog.far)

  // 初始化相机
  const config = defaultSceneConfig.camera
  camera = new THREE.PerspectiveCamera(
    config.fov,
    window.innerWidth / window.innerHeight,
    config.near,
    config.far
  )

  // 初始化渲染器
  renderer = new THREE.WebGLRenderer({
    antialias: true,
    alpha: true
  })
  renderer.setSize(window.innerWidth, window.innerHeight)
  renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFSoftShadowMap
  renderer.outputColorSpace = THREE.SRGBColorSpace
  container.value?.appendChild(renderer.domElement)

  // 初始化光照
  initLighting()

  // 初始化交互
  initInteraction()

  // 设置相机位置
  const pos = config.initialPosition
  camera.position.set(pos.x, pos.y, pos.z)
  camera.lookAt(0, 0, 0)

  // 添加控制器
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true
  controls.dampingFactor = 0.05
  controls.maxPolarAngle = Math.PI / 2 - 0.1
  controls.minDistance = 10
  controls.maxDistance = 500
}

// 初始化光照系统
const initLighting = () => {
  const lightConfig = defaultSceneConfig.lighting

  // 环境光
  const ambientLight = new THREE.AmbientLight(0xffffff, lightConfig.ambientIntensity)
  scene.add(ambientLight)

  // 主平行光
  const directionalLight = new THREE.DirectionalLight(0xffffff, lightConfig.directionalIntensity)
  directionalLight.position.set(50, 50, 50)
  directionalLight.castShadow = true
  directionalLight.shadow.mapSize.width = lightConfig.shadowMapSize
  directionalLight.shadow.mapSize.height = lightConfig.shadowMapSize
  directionalLight.shadow.camera.near = 0.5
  directionalLight.shadow.camera.far = 500
  directionalLight.shadow.camera.left = -100
  directionalLight.shadow.camera.right = 100
  directionalLight.shadow.camera.top = 100
  directionalLight.shadow.camera.bottom = -100
  scene.add(directionalLight)

  // 辅助光源
  const fillLight = new THREE.DirectionalLight(0x87ceeb, 0.3)
  fillLight.position.set(-30, 20, -30)
  scene.add(fillLight)
}

// 初始化交互系统
const initInteraction = () => {
  raycaster = new THREE.Raycaster()
  mouse = new THREE.Vector2()

  // 添加鼠标事件监听
  renderer.domElement.addEventListener('click', onMouseClick)
  renderer.domElement.addEventListener('mousemove', onMouseMove)
}

// 创建地形
const createTerrain = () => {
  const config = defaultSceneConfig.terrain
  const terrainGeometry = new THREE.PlaneGeometry(
    config.size,
    config.size,
    config.segments,
    config.segments
  )

  // 创建地形材质
  const terrainMaterial = new THREE.MeshLambertMaterial({
    color: 0x3a5f0b,
    wireframe: false,
  })

  // 添加地形起伏
  const vertices = terrainGeometry.attributes.position.array as Float32Array
  for (let i = 0; i < vertices.length; i += 3) {
    const x = vertices[i]
    const z = vertices[i + 2]
    vertices[i + 1] = Math.sin(x / 10) * Math.cos(z / 10) * config.heightVariation
  }
  terrainGeometry.computeVertexNormals()

  const terrain = new THREE.Mesh(terrainGeometry, terrainMaterial)
  terrain.rotation.x = -Math.PI / 2
  terrain.receiveShadow = true
  terrain.name = 'terrain'
  scene.add(terrain)
}

// 创建天空盒
const createSkybox = () => {
  const skyGeometry = new THREE.SphereGeometry(800, 32, 32)

  // 创建渐变天空材质
  const skyMaterial = new THREE.ShaderMaterial({
    uniforms: {
      topColor: { value: new THREE.Color(0x0077ff) },
      bottomColor: { value: new THREE.Color(0xffffff) },
      offset: { value: 33 },
      exponent: { value: 0.6 }
    },
    vertexShader: `
      varying vec3 vWorldPosition;
      void main() {
        vec4 worldPosition = modelMatrix * vec4(position, 1.0);
        vWorldPosition = worldPosition.xyz;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `,
    fragmentShader: `
      uniform vec3 topColor;
      uniform vec3 bottomColor;
      uniform float offset;
      uniform float exponent;
      varying vec3 vWorldPosition;
      void main() {
        float h = normalize(vWorldPosition + offset).y;
        gl_FragColor = vec4(mix(bottomColor, topColor, max(pow(max(h, 0.0), exponent), 0.0)), 1.0);
      }
    `,
    side: THREE.BackSide
  })

  const sky = new THREE.Mesh(skyGeometry, skyMaterial)
  sky.name = 'skybox'
  scene.add(sky)
}

// 创建云层
const createClouds = () => {
  const cloudGroup = new THREE.Group()
  cloudGroup.name = 'clouds'

  for (let i = 0; i < 20; i++) {
    const cloudGeometry = new THREE.SphereGeometry(
      Math.random() * 10 + 5,
      8,
      6
    )
    const cloudMaterial = new THREE.MeshLambertMaterial({
      color: 0xffffff,
      transparent: true,
      opacity: 0.6
    })

    const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial)
    cloud.position.set(
      (Math.random() - 0.5) * 400,
      Math.random() * 30 + 40,
      (Math.random() - 0.5) * 400
    )
    cloud.scale.set(
      Math.random() * 2 + 1,
      Math.random() * 0.5 + 0.5,
      Math.random() * 2 + 1
    )

    cloudGroup.add(cloud)
  }

  scene.add(cloudGroup)
}

// 创建监测站点
const createMonitoringStations = () => {
  // 清除现有站点
  stationMeshes.forEach(mesh => scene.remove(mesh))
  stationMeshes.clear()

  props.stations.forEach(station => {
    const stationGroup = new THREE.Group()
    stationGroup.name = `station-${station.id}`

    // 站点主体
    const stationGeometry = new THREE.CylinderGeometry(1, 1, 4, 8)
    const stationMaterial = new THREE.MeshPhongMaterial({
      color: stationIconConfig.color
    })
    const stationMesh = new THREE.Mesh(stationGeometry, stationMaterial)
    stationMesh.position.y = 2
    stationMesh.castShadow = true
    stationGroup.add(stationMesh)

    // 站点顶部天线
    const antennaGeometry = new THREE.CylinderGeometry(0.1, 0.1, 2, 4)
    const antennaMaterial = new THREE.MeshPhongMaterial({ color: 0x666666 })
    const antenna = new THREE.Mesh(antennaGeometry, antennaMaterial)
    antenna.position.y = 5
    stationGroup.add(antenna)

    // 设置站点位置
    stationGroup.position.set(
      station.position.x,
      station.position.y,
      station.position.z
    )

    // 添加用户数据
    stationGroup.userData = { station, type: 'station' }

    scene.add(stationGroup)
    stationMeshes.set(station.id, stationGroup)
  })
}

// 创建预警区域
const createWarningAreas = () => {
  // 清除现有预警区域
  warningAreaMeshes.forEach(mesh => scene.remove(mesh))
  warningAreaMeshes.clear()

  props.warnings.forEach(warning => {
    if (!props.controlState.activeWarnings.has(warning.type)) return

    const areaGeometry = new THREE.CylinderGeometry(
      warning.radius,
      warning.radius,
      0.5,
      32
    )

    const config = defaultWeatherEffects[warning.type]
    const areaMaterial = new THREE.MeshBasicMaterial({
      color: config.color,
      transparent: true,
      opacity: 0.3 * warning.intensity,
      side: THREE.DoubleSide
    })

    const areaMesh = new THREE.Mesh(areaGeometry, areaMaterial)
    areaMesh.position.set(
      warning.center.x,
      warning.center.y + 0.25,
      warning.center.z
    )
    areaMesh.name = `warning-${warning.id}`
    areaMesh.userData = { warning, type: 'warning' }

    scene.add(areaMesh)
    warningAreaMeshes.set(warning.id, areaMesh)
  })
}

// 创建粒子系统
const createParticleSystem = (type: WeatherWarningType, warning: WeatherWarningArea) => {
  const config = defaultWeatherEffects[type]
  const particleConfig = config.particles

  const geometry = new THREE.BufferGeometry()
  const vertices: number[] = []
  const velocities: number[] = []

  // 在预警区域内生成粒子
  for (let i = 0; i < particleConfig.count; i++) {
    const angle = Math.random() * Math.PI * 2
    const radius = Math.random() * warning.radius
    const x = warning.center.x + Math.cos(angle) * radius
    const y = warning.center.y + Math.random() * 20
    const z = warning.center.z + Math.sin(angle) * radius

    vertices.push(x, y, z)

    // 根据预警类型设置不同的速度
    let vx = particleConfig.velocity.x
    let vy = particleConfig.velocity.y
    let vz = particleConfig.velocity.z

    if (type === WeatherWarningType.STRONG_WIND || type === WeatherWarningType.GALE) {
      // 风类型：添加旋转效果
      const windAngle = Math.atan2(z - warning.center.z, x - warning.center.x)
      vx = Math.cos(windAngle + Math.PI / 2) * particleConfig.velocity.x
      vz = Math.sin(windAngle + Math.PI / 2) * particleConfig.velocity.z
    }

    velocities.push(
      vx + (Math.random() - 0.5) * 0.1,
      vy + (Math.random() - 0.5) * 0.1,
      vz + (Math.random() - 0.5) * 0.1
    )
  }

  geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3))

  const material = new THREE.PointsMaterial({
    color: particleConfig.color,
    size: particleConfig.size,
    transparent: true,
    opacity: particleConfig.opacity * warning.intensity,
    blending: THREE.AdditiveBlending
  })

  const particles = new THREE.Points(geometry, material) as any
  particles.velocities = velocities
  particles.userData = { type, warning }
  particles.name = `particles-${type}-${warning.id}`

  scene.add(particles)

  // 存储到对应类型的数组中
  if (!particleSystems.has(type)) {
    particleSystems.set(type, [])
  }
  particleSystems.get(type)!.push(particles)
}

// 鼠标点击事件处理
const onMouseClick = (event: MouseEvent) => {
  const rect = renderer.domElement.getBoundingClientRect()
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

  raycaster.setFromCamera(mouse, camera)

  // 检测站点点击
  const stationObjects = Array.from(stationMeshes.values())
  const stationIntersects = raycaster.intersectObjects(stationObjects, true)

  if (stationIntersects.length > 0) {
    const intersected = stationIntersects[0].object.parent || stationIntersects[0].object
    const stationData = intersected.userData.station
    if (stationData) {
      emit('stationClick', stationData)
      return
    }
  }

  // 检测预警区域点击
  const warningObjects = Array.from(warningAreaMeshes.values())
  const warningIntersects = raycaster.intersectObjects(warningObjects)

  if (warningIntersects.length > 0) {
    const warningData = warningIntersects[0].object.userData.warning
    if (warningData) {
      emit('warningAreaClick', warningData)
    }
  }
}

// 鼠标移动事件处理
const onMouseMove = (event: MouseEvent) => {
  const rect = renderer.domElement.getBoundingClientRect()
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

  raycaster.setFromCamera(mouse, camera)

  // 检测悬停效果
  const stationObjects = Array.from(stationMeshes.values())
  const intersects = raycaster.intersectObjects(stationObjects, true)

  // 重置所有站点颜色
  stationMeshes.forEach(station => {
    const mesh = station.children[0] as THREE.Mesh
    const material = mesh.material as THREE.MeshPhongMaterial
    material.color.setHex(stationIconConfig.color)
  })

  // 设置悬停站点颜色
  if (intersects.length > 0) {
    const intersected = intersects[0].object.parent || intersects[0].object
    const mesh = intersected.children[0] as THREE.Mesh
    const material = mesh.material as THREE.MeshPhongMaterial
    material.color.setHex(stationIconConfig.hoverColor)
    renderer.domElement.style.cursor = 'pointer'
  } else {
    renderer.domElement.style.cursor = 'default'
  }
}

// 更新粒子系统
const updateParticles = () => {
  const delta = clock.getDelta()

  particleSystems.forEach((particles, type) => {
    particles.forEach(particleSystem => {
      const positions = particleSystem.geometry.attributes.position.array as Float32Array
      const warning = particleSystem.userData.warning as WeatherWarningArea

      for (let i = 0; i < positions.length; i += 3) {
        const vIndex = i / 3

        // 更新位置
        positions[i] += particleSystem.velocities[vIndex * 3] * delta * 60
        positions[i + 1] += particleSystem.velocities[vIndex * 3 + 1] * delta * 60
        positions[i + 2] += particleSystem.velocities[vIndex * 3 + 2] * delta * 60

        // 根据预警类型处理边界
        if (type === WeatherWarningType.HEAVY_RAIN || type === WeatherWarningType.TORRENTIAL_RAIN) {
          // 雨滴效果：从上方重新生成
          if (positions[i + 1] < warning.center.y - 5) {
            const angle = Math.random() * Math.PI * 2
            const radius = Math.random() * warning.radius
            positions[i] = warning.center.x + Math.cos(angle) * radius
            positions[i + 1] = warning.center.y + 20 + Math.random() * 10
            positions[i + 2] = warning.center.z + Math.sin(angle) * radius
          }
        } else if (type === WeatherWarningType.STRONG_WIND || type === WeatherWarningType.GALE) {
          // 风效果：保持在预警区域内循环
          const dx = positions[i] - warning.center.x
          const dz = positions[i + 2] - warning.center.z
          const distance = Math.sqrt(dx * dx + dz * dz)

          if (distance > warning.radius) {
            const angle = Math.atan2(dz, dx)
            positions[i] = warning.center.x + Math.cos(angle + Math.PI) * (warning.radius * 0.8)
            positions[i + 2] = warning.center.z + Math.sin(angle + Math.PI) * (warning.radius * 0.8)
          }

          if (positions[i + 1] > warning.center.y + 15) {
            positions[i + 1] = warning.center.y
          }
        }
      }

      particleSystem.geometry.attributes.position.needsUpdate = true
    })
  })
}

// 更新气象效果
const updateWeatherEffects = () => {
  // 清除所有粒子系统
  particleSystems.forEach(particles => {
    particles.forEach(particle => {
      scene.remove(particle)
      particle.geometry.dispose()
      if (particle.material instanceof THREE.Material) {
        particle.material.dispose()
      }
    })
  })
  particleSystems.clear()

  // 根据当前预警创建新的粒子系统
  props.warnings.forEach(warning => {
    if (props.controlState.activeWarnings.has(warning.type)) {
      createParticleSystem(warning.type, warning)
    }
  })
}

// 动画循环
const animate = () => {
  frameId = requestAnimationFrame(animate)

  updateParticles()
  controls.update()

  // 更新云层动画
  const clouds = scene.getObjectByName('clouds')
  if (clouds) {
    clouds.rotation.y += 0.0005
  }

  renderer.render(scene, camera)
}

// 清理场景
const cleanupScene = () => {
  if (frameId) {
    cancelAnimationFrame(frameId)
  }

  // 移除事件监听器
  if (renderer && renderer.domElement) {
    renderer.domElement.removeEventListener('click', onMouseClick)
    renderer.domElement.removeEventListener('mousemove', onMouseMove)
  }

  // 清理粒子系统
  particleSystems.forEach(particles => {
    particles.forEach(particle => {
      scene.remove(particle)
      particle.geometry.dispose()
      if (particle.material instanceof THREE.Material) {
        particle.material.dispose()
      }
    })
  })
  particleSystems.clear()

  // 清理几何体和材质
  scene.traverse((object) => {
    if (object instanceof THREE.Mesh) {
      object.geometry.dispose()
      if (Array.isArray(object.material)) {
        object.material.forEach(material => material.dispose())
      } else {
        object.material.dispose()
      }
    }
  })

  // 清理渲染器
  if (renderer) {
    renderer.dispose()
  }

  // 清理控制器
  if (controls) {
    controls.dispose()
  }
}

// 窗口大小调整
const handleResize = () => {
  if (camera && renderer) {
    camera.aspect = window.innerWidth / window.innerHeight
    camera.updateProjectionMatrix()
    renderer.setSize(window.innerWidth, window.innerHeight)
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
  }
}

// 初始化完整场景
const initCompleteScene = () => {
  initScene()
  createTerrain()
  createSkybox()
  createClouds()
  createMonitoringStations()
  createWarningAreas()
  updateWeatherEffects()
}

// 监听props变化
watch(() => props.stations, () => {
  createMonitoringStations()
}, { deep: true })

watch(() => props.warnings, () => {
  createWarningAreas()
  updateWeatherEffects()
}, { deep: true })

watch(() => props.controlState.activeWarnings, () => {
  createWarningAreas()
  updateWeatherEffects()
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  initCompleteScene()
  animate()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  cleanupScene()
})

// 暴露给父组件的方法
defineExpose({
  updateWeatherEffects,
  createMonitoringStations,
  createWarningAreas
})
</script>

<template>
  <div ref="container" class="w-full h-full"></div>
</template>

<style scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style> 