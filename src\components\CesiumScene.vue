<template>
  <div ref="cesiumContainer" class="w-full h-full relative">
    <!-- Cesium容器 -->
    <div id="cesiumContainer" class="w-full h-full"></div>
    
    <!-- 加载指示器 -->
    <div 
      v-if="loading" 
      class="absolute inset-0 bg-black/50 flex items-center justify-center z-10"
    >
      <div class="bg-white rounded-lg p-6 text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-700">正在加载珠海市三维地图...</p>
      </div>
    </div>
    
    <!-- 信息面板 -->
    <div 
      v-if="selectedEntity" 
      class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg max-w-sm z-20"
    >
      <h3 class="font-bold text-lg mb-2">{{ selectedEntity.name }}</h3>
      <div class="space-y-1 text-sm">
        <div v-if="selectedEntity.type === 'station'">
          <p><strong>类型:</strong> 监测站点</p>
          <p><strong>温度:</strong> {{ selectedEntity.data.temperature }}°C</p>
          <p><strong>湿度:</strong> {{ selectedEntity.data.humidity }}%</p>
          <p><strong>风速:</strong> {{ selectedEntity.data.windSpeed }} m/s</p>
          <p><strong>气压:</strong> {{ selectedEntity.data.pressure }} hPa</p>
        </div>
        <div v-else-if="selectedEntity.type === 'warning'">
          <p><strong>类型:</strong> {{ getWarningTypeName(selectedEntity.data.type) }}</p>
          <p><strong>级别:</strong> {{ selectedEntity.data.level.toUpperCase() }}</p>
          <p><strong>强度:</strong> {{ Math.round(selectedEntity.data.intensity * 100) }}%</p>
          <p><strong>半径:</strong> {{ selectedEntity.data.radius }}m</p>
        </div>
      </div>
      <button 
        @click="selectedEntity = null"
        class="mt-3 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
      >
        关闭
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as Cesium from 'cesium'
import { WeatherWarningType } from '@/types/weather'
import type {
  WeatherWarningArea,
  MonitoringStation,
  ControlPanelState,
  GeographicPosition
} from '@/types/weather'
import { 
  defaultCesiumConfig, 
  zhuhaiConfig, 
  cesiumWarningColors,
  stationIconConfig 
} from '@/config/zhuhaiConfig'

// Props
interface Props {
  warnings: WeatherWarningArea[]
  stations: MonitoringStation[]
  controlState: ControlPanelState
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  stationClick: [station: MonitoringStation]
  warningAreaClick: [warning: WeatherWarningArea]
}>()

// 响应式数据
const cesiumContainer = ref<HTMLDivElement | null>(null)
const loading = ref(true)
const selectedEntity = ref<any>(null)

// Cesium相关变量
let viewer: Cesium.Viewer | null = null
let stationEntities: Map<string, Cesium.Entity> = new Map()
let warningEntities: Map<string, Cesium.Entity> = new Map()

// 初始化Cesium场景
const initCesiumScene = async () => {
  try {
    // 设置Cesium访问令牌（如果需要）
    // Cesium.Ion.defaultAccessToken = 'your-access-token'
    
    // 创建Cesium Viewer
    viewer = new Cesium.Viewer('cesiumContainer', {
      terrainProvider: await Cesium.createWorldTerrainAsync(),
      imageryProvider: new Cesium.ArcGisMapServerImageryProvider({
        url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer'
      }),
      baseLayerPicker: false,
      geocoder: false,
      homeButton: false,
      sceneModePicker: false,
      navigationHelpButton: false,
      animation: false,
      timeline: false,
      fullscreenButton: false,
      vrButton: false,
      requestRenderMode: true,
      maximumRenderTimeChange: Infinity
    })
    
    // 设置相机位置到珠海
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        defaultCesiumConfig.camera.destination.longitude,
        defaultCesiumConfig.camera.destination.latitude,
        defaultCesiumConfig.camera.destination.height
      ),
      orientation: {
        heading: defaultCesiumConfig.camera.orientation.heading,
        pitch: defaultCesiumConfig.camera.orientation.pitch,
        roll: defaultCesiumConfig.camera.orientation.roll
      }
    })
    
    // 启用光照
    viewer.scene.globe.enableLighting = true
    
    // 设置大气效果
    viewer.scene.skyAtmosphere.show = defaultCesiumConfig.atmosphere.show
    
    // 添加点击事件监听
    viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(
      onEntityClick,
      Cesium.ScreenSpaceEventType.LEFT_CLICK
    )
    
    // 添加珠海市边界
    addZhuhaiBoundary()
    
    loading.value = false
    
  } catch (error) {
    console.error('初始化Cesium场景失败:', error)
    loading.value = false
  }
}

// 添加珠海市边界
const addZhuhaiBoundary = () => {
  if (!viewer) return
  
  zhuhaiConfig.districts.forEach(district => {
    const positions = district.boundary.map(pos => 
      Cesium.Cartesian3.fromDegrees(pos.longitude, pos.latitude, pos.height || 0)
    )
    
    viewer!.entities.add({
      name: district.name,
      polygon: {
        hierarchy: positions,
        material: Cesium.Color.YELLOW.withAlpha(0.1),
        outline: true,
        outlineColor: Cesium.Color.YELLOW,
        height: 0
      }
    })
  })
}

// 创建监测站点
const createStations = () => {
  if (!viewer) return
  
  // 清除现有站点
  stationEntities.forEach(entity => viewer!.entities.remove(entity))
  stationEntities.clear()
  
  props.stations.forEach(station => {
    const entity = viewer!.entities.add({
      id: `station-${station.id}`,
      name: station.name,
      position: Cesium.Cartesian3.fromDegrees(
        station.position.longitude,
        station.position.latitude,
        station.position.height || 0
      ),
      billboard: {
        image: '/icons/weather-station.png',
        scale: 1.0,
        color: Cesium.Color.fromCssColorString(stationIconConfig.normal.color),
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      },
      label: {
        text: station.name,
        font: '12pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -50),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    })
    
    // 添加自定义属性
    entity.customData = {
      type: 'station',
      data: station
    }
    
    stationEntities.set(station.id, entity)
  })
}

// 创建预警区域
const createWarningAreas = () => {
  if (!viewer) return
  
  // 清除现有预警区域
  warningEntities.forEach(entity => viewer!.entities.remove(entity))
  warningEntities.clear()
  
  props.warnings.forEach(warning => {
    if (!props.controlState.activeWarnings.has(warning.type)) return
    
    const colorConfig = cesiumWarningColors[warning.type]
    
    const entity = viewer!.entities.add({
      id: `warning-${warning.id}`,
      name: `${getWarningTypeName(warning.type)} - ${warning.level.toUpperCase()}`,
      position: Cesium.Cartesian3.fromDegrees(
        warning.center.longitude,
        warning.center.latitude,
        warning.center.height || 0
      ),
      ellipse: {
        semiMajorAxis: warning.radius,
        semiMinorAxis: warning.radius,
        material: Cesium.Color.fromCssColorString(colorConfig.color),
        outline: true,
        outlineColor: Cesium.Color.fromCssColorString(colorConfig.outlineColor),
        height: 100,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    })
    
    // 添加自定义属性
    entity.customData = {
      type: 'warning',
      data: warning
    }
    
    warningEntities.set(warning.id, entity)
  })
}

// 实体点击事件处理
const onEntityClick = (event: any) => {
  const pickedObject = viewer!.scene.pick(event.position)
  
  if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.id)) {
    const entity = pickedObject.id
    
    if (entity.customData) {
      selectedEntity.value = {
        name: entity.name,
        type: entity.customData.type,
        data: entity.customData.data
      }
      
      // 触发相应的事件
      if (entity.customData.type === 'station') {
        emit('stationClick', entity.customData.data)
      } else if (entity.customData.type === 'warning') {
        emit('warningAreaClick', entity.customData.data)
      }
    }
  }
}

// 获取预警类型名称
const getWarningTypeName = (type: WeatherWarningType): string => {
  const names = {
    [WeatherWarningType.HEAVY_RAIN]: '暴雨预警',
    [WeatherWarningType.STRONG_WIND]: '狂风预警',
    [WeatherWarningType.GALE]: '强风预警',
    [WeatherWarningType.TORRENTIAL_RAIN]: '强降雨预警'
  }
  return names[type] || '未知预警'
}

// 监听数据变化
watch(() => props.stations, createStations, { deep: true })
watch(() => props.warnings, createWarningAreas, { deep: true })
watch(() => props.controlState.activeWarnings, createWarningAreas, { deep: true })

// 生命周期
onMounted(async () => {
  await initCesiumScene()
  createStations()
  createWarningAreas()
})

onBeforeUnmount(() => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})

// 暴露方法
defineExpose({
  flyToPosition: (position: GeographicPosition, height = 10000) => {
    if (viewer) {
      viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
          position.longitude,
          position.latitude,
          height
        )
      })
    }
  },
  resetView: () => {
    if (viewer) {
      viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
          defaultCesiumConfig.camera.destination.longitude,
          defaultCesiumConfig.camera.destination.latitude,
          defaultCesiumConfig.camera.destination.height
        )
      })
    }
  }
})
</script>

<style scoped>
/* Cesium样式覆盖 */
:deep(.cesium-viewer-bottom) {
  display: none;
}

:deep(.cesium-widget-credits) {
  display: none;
}
</style>
