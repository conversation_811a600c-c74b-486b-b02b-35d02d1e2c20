<template>
  <div class="weather-data-panel">
    <!-- 实时数据卡片 -->
    <div class="data-cards">
      <div class="data-card temperature">
        <div class="card-header">
          <span class="card-icon">🌡️</span>
          <span class="card-title">温度</span>
        </div>
        <div class="card-value">{{ averageTemperature }}°C</div>
        <div class="card-trend" :class="temperatureTrend">
          {{ temperatureTrend === 'up' ? '↗️' : temperatureTrend === 'down' ? '↘️' : '➡️' }}
          {{ getTemperatureRange() }}
        </div>
      </div>

      <div class="data-card humidity">
        <div class="card-header">
          <span class="card-icon">💧</span>
          <span class="card-title">湿度</span>
        </div>
        <div class="card-value">{{ averageHumidity }}%</div>
        <div class="card-trend">
          <div class="humidity-bar">
            <div class="humidity-fill" :style="{ width: averageHumidity + '%' }"></div>
          </div>
        </div>
      </div>

      <div class="data-card wind">
        <div class="card-header">
          <span class="card-icon">💨</span>
          <span class="card-title">风速</span>
        </div>
        <div class="card-value">{{ averageWindSpeed }} m/s</div>
        <div class="card-trend">
          <div class="wind-direction" :style="{ transform: `rotate(${averageWindDirection}deg)` }">
            ↑
          </div>
          <span class="wind-level">{{ getWindLevel() }}</span>
        </div>
      </div>

      <div class="data-card pressure">
        <div class="card-header">
          <span class="card-icon">📊</span>
          <span class="card-title">气压</span>
        </div>
        <div class="card-value">{{ averagePressure }} hPa</div>
        <div class="card-trend">
          <div class="pressure-indicator" :class="getPressureStatus()">
            {{ getPressureStatus() === 'high' ? '高压' : getPressureStatus() === 'low' ? '低压' : '正常' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 预警信息 -->
    <div class="warnings-section" v-if="activeWarnings.length > 0">
      <h3 class="section-title">⚠️ 活跃预警</h3>
      <div class="warnings-list">
        <div 
          v-for="warning in activeWarnings" 
          :key="warning.id"
          class="warning-item"
          :class="getWarningLevelClass(warning.level)"
        >
          <div class="warning-header">
            <span class="warning-type-icon">{{ getWarningIcon(warning.type) }}</span>
            <span class="warning-type">{{ getWarningTypeName(warning.type) }}</span>
            <span class="warning-level">{{ getWarningLevelName(warning.level) }}</span>
          </div>
          <div class="warning-description">{{ warning.description }}</div>
          <div class="warning-time">
            <span class="time-label">有效期:</span>
            <span class="time-value">{{ formatTime(warning.endTime) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 站点状态 -->
    <div class="stations-section">
      <h3 class="section-title">📍 监测站点状态</h3>
      <div class="stations-list">
        <div 
          v-for="station in stations" 
          :key="station.id"
          class="station-item"
          @click="$emit('station-select', station)"
        >
          <div class="station-header">
            <span class="station-name">{{ station.name }}</span>
            <span class="station-status" :class="getStationStatus(station)">
              {{ getStationStatusText(station) }}
            </span>
          </div>
          <div class="station-data">
            <div class="data-item">
              <span class="data-label">温度:</span>
              <span class="data-value">{{ station.temperature.toFixed(1) }}°C</span>
            </div>
            <div class="data-item">
              <span class="data-label">湿度:</span>
              <span class="data-value">{{ station.humidity }}%</span>
            </div>
            <div class="data-item">
              <span class="data-label">风速:</span>
              <span class="data-value">{{ station.windSpeed.toFixed(1) }} m/s</span>
            </div>
          </div>
          <div class="station-update">
            更新时间: {{ formatUpdateTime(station.lastUpdate) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { WeatherWarningArea, MonitoringStation } from '@/types/weather'
import { WeatherWarningType, WarningLevel } from '@/types/weather'

// Props
interface Props {
  warnings: WeatherWarningArea[]
  stations: MonitoringStation[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'station-select': [station: MonitoringStation]
}>()

// 计算属性
const activeWarnings = computed(() => 
  props.warnings.filter(w => w.isActive)
)

const averageTemperature = computed(() => {
  if (props.stations.length === 0) return 0
  const sum = props.stations.reduce((acc, station) => acc + station.temperature, 0)
  return (sum / props.stations.length).toFixed(1)
})

const averageHumidity = computed(() => {
  if (props.stations.length === 0) return 0
  const sum = props.stations.reduce((acc, station) => acc + station.humidity, 0)
  return Math.round(sum / props.stations.length)
})

const averageWindSpeed = computed(() => {
  if (props.stations.length === 0) return 0
  const sum = props.stations.reduce((acc, station) => acc + station.windSpeed, 0)
  return (sum / props.stations.length).toFixed(1)
})

const averageWindDirection = computed(() => {
  if (props.stations.length === 0) return 0
  const sum = props.stations.reduce((acc, station) => acc + station.windDirection, 0)
  return Math.round(sum / props.stations.length)
})

const averagePressure = computed(() => {
  if (props.stations.length === 0) return 0
  const sum = props.stations.reduce((acc, station) => acc + station.pressure, 0)
  return (sum / props.stations.length).toFixed(1)
})

const temperatureTrend = computed(() => {
  // 简单的趋势计算，实际应该基于历史数据
  const temps = props.stations.map(s => s.temperature)
  const avg = temps.reduce((a, b) => a + b, 0) / temps.length
  const recent = temps.slice(-2).reduce((a, b) => a + b, 0) / Math.min(2, temps.length)
  
  if (recent > avg + 1) return 'up'
  if (recent < avg - 1) return 'down'
  return 'stable'
})

// 方法
const getTemperatureRange = () => {
  if (props.stations.length === 0) return '0°C - 0°C'
  const temps = props.stations.map(s => s.temperature)
  const min = Math.min(...temps).toFixed(1)
  const max = Math.max(...temps).toFixed(1)
  return `${min}°C - ${max}°C`
}

const getWindLevel = () => {
  const speed = parseFloat(averageWindSpeed.value)
  if (speed < 3.4) return '微风'
  if (speed < 5.5) return '轻风'
  if (speed < 8.0) return '微风'
  if (speed < 10.8) return '和风'
  if (speed < 13.9) return '清风'
  if (speed < 17.2) return '强风'
  return '疾风'
}

const getPressureStatus = () => {
  const pressure = parseFloat(averagePressure.value)
  if (pressure > 1020) return 'high'
  if (pressure < 1000) return 'low'
  return 'normal'
}

const getWarningIcon = (type: WeatherWarningType) => {
  const icons = {
    [WeatherWarningType.HEAVY_RAIN]: '🌧️',
    [WeatherWarningType.STRONG_WIND]: '💨',
    [WeatherWarningType.GALE]: '🌪️',
    [WeatherWarningType.TORRENTIAL_RAIN]: '⛈️'
  }
  return icons[type] || '⚠️'
}

const getWarningTypeName = (type: WeatherWarningType) => {
  const names = {
    [WeatherWarningType.HEAVY_RAIN]: '暴雨预警',
    [WeatherWarningType.STRONG_WIND]: '大风预警',
    [WeatherWarningType.GALE]: '强风预警',
    [WeatherWarningType.TORRENTIAL_RAIN]: '强降雨预警'
  }
  return names[type] || '未知预警'
}

const getWarningLevelName = (level: WarningLevel) => {
  const names = {
    [WarningLevel.BLUE]: '蓝色',
    [WarningLevel.YELLOW]: '黄色',
    [WarningLevel.ORANGE]: '橙色',
    [WarningLevel.RED]: '红色'
  }
  return names[level] || '未知'
}

const getWarningLevelClass = (level: WarningLevel) => {
  return level.toLowerCase()
}

const getStationStatus = (station: MonitoringStation) => {
  const now = new Date()
  const lastUpdate = new Date(station.lastUpdate)
  const diffMinutes = (now.getTime() - lastUpdate.getTime()) / (1000 * 60)
  
  if (diffMinutes > 60) return 'offline'
  if (diffMinutes > 30) return 'warning'
  return 'online'
}

const getStationStatusText = (station: MonitoringStation) => {
  const status = getStationStatus(station)
  const texts = {
    online: '在线',
    warning: '延迟',
    offline: '离线'
  }
  return texts[status] || '未知'
}

const formatTime = (date: Date) => {
  return new Date(date).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatUpdateTime = (date: Date) => {
  return new Date(date).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.weather-data-panel {
  position: fixed;
  bottom: 20px;
  left: 20px;
  width: 400px;
  max-height: 70vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  font-family: 'Microsoft YaHei', sans-serif;
  z-index: 900;
}

/* 数据卡片 */
.data-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1px;
  background: #e5e7eb;
}

.data-card {
  background: white;
  padding: 16px;
  position: relative;
  overflow: hidden;
}

.data-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
}

.data-card.temperature::before { background: linear-gradient(90deg, #f59e0b, #ef4444); }
.data-card.humidity::before { background: linear-gradient(90deg, #3b82f6, #1d4ed8); }
.data-card.wind::before { background: linear-gradient(90deg, #10b981, #059669); }
.data-card.pressure::before { background: linear-gradient(90deg, #8b5cf6, #7c3aed); }

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.card-icon {
  font-size: 16px;
}

.card-title {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.humidity-bar {
  width: 60px;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.humidity-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transition: width 0.3s ease;
}

.wind-direction {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.wind-level {
  font-size: 11px;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.pressure-indicator {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.pressure-indicator.high { background: #fef3c7; color: #92400e; }
.pressure-indicator.low { background: #dbeafe; color: #1e40af; }
.pressure-indicator.normal { background: #d1fae5; color: #065f46; }

/* 预警区域 */
.warnings-section, .stations-section {
  padding: 16px 20px;
  border-top: 1px solid #f3f4f6;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.warnings-list, .stations-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.warning-item {
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid;
}

.warning-item.blue { 
  background: #eff6ff; 
  border-left-color: #3b82f6; 
}

.warning-item.yellow { 
  background: #fffbeb; 
  border-left-color: #f59e0b; 
}

.warning-item.orange { 
  background: #fff7ed; 
  border-left-color: #ea580c; 
}

.warning-item.red { 
  background: #fef2f2; 
  border-left-color: #ef4444; 
}

.warning-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.warning-type-icon {
  font-size: 16px;
}

.warning-type {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
}

.warning-level {
  font-size: 11px;
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: auto;
}

.warning-description {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 6px;
  line-height: 1.4;
}

.warning-time {
  font-size: 11px;
  color: #9ca3af;
}

.time-label {
  margin-right: 4px;
}

/* 站点区域 */
.station-item {
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.station-item:hover {
  background: #f3f4f6;
  transform: translateY(-1px);
}

.station-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.station-name {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
}

.station-status {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.station-status.online { background: #d1fae5; color: #065f46; }
.station-status.warning { background: #fef3c7; color: #92400e; }
.station-status.offline { background: #fef2f2; color: #991b1b; }

.station-data {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 6px;
}

.data-item {
  text-align: center;
}

.data-label {
  font-size: 10px;
  color: #9ca3af;
  display: block;
}

.data-value {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.station-update {
  font-size: 10px;
  color: #9ca3af;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .weather-data-panel {
    left: 10px;
    right: 10px;
    width: auto;
    bottom: 10px;
  }
  
  .data-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .card-value {
    font-size: 20px;
  }
}

/* 滚动条样式 */
.warnings-list::-webkit-scrollbar,
.stations-list::-webkit-scrollbar {
  width: 4px;
}

.warnings-list::-webkit-scrollbar-track,
.stations-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.warnings-list::-webkit-scrollbar-thumb,
.stations-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}
</style>
