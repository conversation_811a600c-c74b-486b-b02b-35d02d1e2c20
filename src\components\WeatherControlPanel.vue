<template>
  <div class="control-panel-container">
    <!-- 主控制面板 -->
    <div class="main-panel" :class="{ 'collapsed': isCollapsed }">
      <!-- 面板头部 -->
      <div class="panel-header">
        <div class="header-content">
          <div class="title-section">
            <h2 class="panel-title">🌦️ 气象预警控制中心</h2>
            <div class="status-indicator">
              <span class="status-dot" :class="systemStatus"></span>
              <span class="status-text">{{ getStatusText() }}</span>
            </div>
          </div>
          <button @click="togglePanel" class="collapse-btn">
            {{ isCollapsed ? '📊' : '📉' }}
          </button>
        </div>
      </div>

      <!-- 面板内容 -->
      <div class="panel-content" v-show="!isCollapsed">
        <!-- 预警统计 -->
        <div class="stats-section">
          <h3 class="section-title">📈 预警统计</h3>
          <div class="stats-grid">
            <div class="stat-card total">
              <div class="stat-number">{{ warningStats.active }}</div>
              <div class="stat-label">活跃预警</div>
            </div>
            <div class="stat-card stations">
              <div class="stat-number">{{ stations.length }}</div>
              <div class="stat-label">监测站点</div>
            </div>
            <div class="stat-card coverage">
              <div class="stat-number">{{ getCoveragePercentage() }}%</div>
              <div class="stat-label">覆盖率</div>
            </div>
          </div>
        </div>

        <!-- 预警类型控制 -->
        <div class="warning-types-section">
          <h3 class="section-title">⚠️ 预警类型</h3>
          <div class="warning-types-grid">
            <div 
              v-for="type in warningTypes" 
              :key="type.key"
              class="warning-type-card"
              :class="{ 'active': activeWarningTypes.has(type.key) }"
              @click="toggleWarningType(type.key)"
            >
              <div class="warning-icon">{{ type.icon }}</div>
              <div class="warning-info">
                <div class="warning-name">{{ type.name }}</div>
                <div class="warning-count">{{ getWarningCount(type.key) }} 个</div>
              </div>
              <div class="warning-toggle">
                <div class="toggle-switch" :class="{ 'on': activeWarningTypes.has(type.key) }"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 视图控制 -->
        <div class="view-controls-section">
          <h3 class="section-title">🎮 视图控制</h3>
          <div class="view-controls-grid">
            <button @click="$emit('focus-zhuhai')" class="control-btn primary">
              🏠 回到珠海
            </button>
            <button @click="$emit('zoom-in')" class="control-btn secondary">
              🔍 放大
            </button>
            <button @click="$emit('zoom-out')" class="control-btn secondary">
              🔍 缩小
            </button>
            <button @click="$emit('reset-view')" class="control-btn accent">
              🔄 重置视角
            </button>
          </div>
        </div>

        <!-- 图层控制 -->
        <div class="layer-controls-section">
          <h3 class="section-title">🗺️ 图层控制</h3>
          <div class="layer-controls">
            <div class="layer-item">
              <label class="layer-label">
                <input 
                  type="checkbox" 
                  v-model="layerSettings.showStations"
                  @change="$emit('toggle-stations', layerSettings.showStations)"
                >
                <span class="checkmark"></span>
                监测站点
              </label>
            </div>
            <div class="layer-item">
              <label class="layer-label">
                <input 
                  type="checkbox" 
                  v-model="layerSettings.showWarnings"
                  @change="$emit('toggle-warnings', layerSettings.showWarnings)"
                >
                <span class="checkmark"></span>
                预警区域
              </label>
            </div>
            <div class="layer-item">
              <label class="layer-label">
                <input 
                  type="checkbox" 
                  v-model="layerSettings.showBoundaries"
                  @change="$emit('toggle-boundaries', layerSettings.showBoundaries)"
                >
                <span class="checkmark"></span>
                行政边界
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作浮动按钮 -->
    <div class="floating-actions">
      <button @click="$emit('emergency-mode')" class="fab emergency" title="紧急模式">
        🚨
      </button>
      <button @click="$emit('refresh-data')" class="fab refresh" title="刷新数据">
        🔄
      </button>
      <button @click="$emit('export-data')" class="fab export" title="导出数据">
        📊
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { WeatherWarningType } from '@/types/weather'
import type { WeatherWarningArea, MonitoringStation } from '@/types/weather'

// Props
interface Props {
  warnings: WeatherWarningArea[]
  stations: MonitoringStation[]
  activeWarningTypes: Set<WeatherWarningType>
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'toggle-warning-type': [type: WeatherWarningType]
  'focus-zhuhai': []
  'zoom-in': []
  'zoom-out': []
  'reset-view': []
  'toggle-stations': [show: boolean]
  'toggle-warnings': [show: boolean]
  'toggle-boundaries': [show: boolean]
  'emergency-mode': []
  'refresh-data': []
  'export-data': []
}>()

// 响应式数据
const isCollapsed = ref(false)
const systemStatus = ref<'online' | 'warning' | 'error'>('online')

const layerSettings = ref({
  showStations: true,
  showWarnings: true,
  showBoundaries: true
})

// 预警类型配置
const warningTypes = [
  { key: WeatherWarningType.HEAVY_RAIN, name: '暴雨预警', icon: '🌧️' },
  { key: WeatherWarningType.STRONG_WIND, name: '大风预警', icon: '💨' },
  { key: WeatherWarningType.GALE, name: '强风预警', icon: '🌪️' },
  { key: WeatherWarningType.TORRENTIAL_RAIN, name: '强降雨', icon: '⛈️' }
]

// 计算属性
const warningStats = computed(() => {
  const active = props.warnings.filter(w => w.isActive)
  return {
    total: props.warnings.length,
    active: active.length,
    byType: {
      [WeatherWarningType.HEAVY_RAIN]: active.filter(w => w.type === WeatherWarningType.HEAVY_RAIN).length,
      [WeatherWarningType.STRONG_WIND]: active.filter(w => w.type === WeatherWarningType.STRONG_WIND).length,
      [WeatherWarningType.GALE]: active.filter(w => w.type === WeatherWarningType.GALE).length,
      [WeatherWarningType.TORRENTIAL_RAIN]: active.filter(w => w.type === WeatherWarningType.TORRENTIAL_RAIN).length
    }
  }
})

// 方法
const togglePanel = () => {
  isCollapsed.value = !isCollapsed.value
}

const toggleWarningType = (type: WeatherWarningType) => {
  emit('toggle-warning-type', type)
}

const getWarningCount = (type: WeatherWarningType) => {
  return warningStats.value.byType[type] || 0
}

const getCoveragePercentage = () => {
  return Math.round((props.stations.length / 10) * 100) // 假设最大10个站点
}

const getStatusText = () => {
  switch (systemStatus.value) {
    case 'online': return '系统正常'
    case 'warning': return '有预警'
    case 'error': return '系统异常'
    default: return '未知状态'
  }
}

// 生命周期
onMounted(() => {
  // 根据预警数量设置系统状态
  if (warningStats.value.active > 0) {
    systemStatus.value = 'warning'
  }
})
</script>

<style scoped>
.control-panel-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  font-family: 'Microsoft YaHei', sans-serif;
}

/* 主面板 */
.main-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 380px;
  max-height: 80vh;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-panel.collapsed {
  width: 200px;
  height: 60px;
}

/* 面板头部 */
.panel-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  opacity: 0.9;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.online { background: #10b981; }
.status-dot.warning { background: #f59e0b; }
.status-dot.error { background: #ef4444; }

.collapse-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.collapse-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* 面板内容 */
.panel-content {
  padding: 20px;
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 统计区域 */
.stats-section {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stat-card {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  padding: 16px 12px;
  text-align: center;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-card.total { background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%); }
.stat-card.stations { background: linear-gradient(135deg, #dbeafe 0%, #3b82f6 100%); }
.stat-card.coverage { background: linear-gradient(135deg, #d1fae5 0%, #10b981 100%); }

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

/* 预警类型区域 */
.warning-types-section {
  margin-bottom: 24px;
}

.warning-types-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.warning-type-card {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 10px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.warning-type-card:hover {
  background: #f3f4f6;
  transform: translateY(-1px);
}

.warning-type-card.active {
  background: #eff6ff;
  border-color: #3b82f6;
}

.warning-icon {
  font-size: 20px;
  margin-right: 12px;
}

.warning-info {
  flex: 1;
}

.warning-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.warning-count {
  font-size: 12px;
  color: #6b7280;
}

.warning-toggle {
  margin-left: 12px;
}

.toggle-switch {
  width: 40px;
  height: 20px;
  background: #d1d5db;
  border-radius: 10px;
  position: relative;
  transition: all 0.2s ease;
}

.toggle-switch::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch.on {
  background: #3b82f6;
}

.toggle-switch.on::after {
  transform: translateX(20px);
}

/* 视图控制区域 */
.view-controls-section {
  margin-bottom: 24px;
}

.view-controls-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.control-btn {
  padding: 10px 12px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-btn.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.control-btn.secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
}

.control-btn.accent {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.control-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 图层控制区域 */
.layer-controls-section {
  margin-bottom: 16px;
}

.layer-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.layer-item {
  display: flex;
  align-items: center;
}

.layer-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.layer-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  background: #f3f4f6;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  margin-right: 8px;
  position: relative;
  transition: all 0.2s ease;
}

.layer-label input[type="checkbox"]:checked + .checkmark {
  background: #3b82f6;
  border-color: #3b82f6;
}

.layer-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  color: white;
  font-size: 12px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 浮动操作按钮 */
.floating-actions {
  position: fixed;
  bottom: 30px;
  right: 30px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 999;
}

.fab {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.fab.emergency {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.fab.refresh {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.fab.export {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

/* 动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-panel-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .main-panel {
    width: 100%;
    max-width: none;
  }
  
  .floating-actions {
    bottom: 20px;
    right: 20px;
  }
  
  .fab {
    width: 48px;
    height: 48px;
    font-size: 18px;
  }
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 4px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
