<template>
  <div class="diagnostic-container">
    <div class="diagnostic-panel">
      <h2>🔧 Cesium 诊断工具</h2>
      
      <div class="test-section">
        <h3>📋 系统检查</h3>
        <div class="test-results">
          <div class="test-item" :class="checks.browser ? 'success' : 'error'">
            <span class="test-icon">{{ checks.browser ? '✅' : '❌' }}</span>
            <span class="test-text">浏览器兼容性</span>
            <span class="test-detail">{{ browserInfo }}</span>
          </div>
          
          <div class="test-item" :class="checks.webgl ? 'success' : 'error'">
            <span class="test-icon">{{ checks.webgl ? '✅' : '❌' }}</span>
            <span class="test-text">WebGL 支持</span>
            <span class="test-detail">{{ webglInfo }}</span>
          </div>
          
          <div class="test-item" :class="checks.network ? 'success' : 'warning'">
            <span class="test-icon">{{ checks.network ? '✅' : '⚠️' }}</span>
            <span class="test-text">网络连接</span>
            <span class="test-detail">{{ networkInfo }}</span>
          </div>
          
          <div class="test-item" :class="checks.cesium ? 'success' : 'error'">
            <span class="test-icon">{{ checks.cesium ? '✅' : '❌' }}</span>
            <span class="test-text">Cesium 模块</span>
            <span class="test-detail">{{ cesiumInfo }}</span>
          </div>
        </div>
      </div>
      
      <div class="test-section">
        <h3>🧪 功能测试</h3>
        <div class="test-buttons">
          <button @click="testCesiumImport" class="test-btn" :disabled="testing">
            {{ testing ? '测试中...' : '测试 Cesium 导入' }}
          </button>
          
          <button @click="testBasicViewer" class="test-btn" :disabled="testing || !cesiumLoaded">
            测试基础 Viewer
          </button>
          
          <button @click="testWithoutToken" class="test-btn" :disabled="testing">
            无Token测试
          </button>
          
          <button @click="useFallbackMode" class="test-btn fallback">
            使用2D模式
          </button>
        </div>
      </div>
      
      <div class="test-section">
        <h3>📝 测试日志</h3>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-entry" :class="log.type">
            <span class="log-time">[{{ log.time }}]</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
      
      <div class="test-section">
        <h3>💡 解决方案</h3>
        <div class="solutions">
          <div v-if="!checks.webgl" class="solution-item error">
            <h4>WebGL 不支持</h4>
            <p>您的浏览器不支持WebGL，这是Cesium运行的必要条件。</p>
            <ul>
              <li>更新到最新版本的Chrome、Firefox或Edge浏览器</li>
              <li>启用硬件加速</li>
              <li>更新显卡驱动程序</li>
            </ul>
          </div>
          
          <div v-if="!checks.network" class="solution-item warning">
            <h4>网络连接问题</h4>
            <p>无法访问Cesium的在线资源。</p>
            <ul>
              <li>检查网络连接</li>
              <li>尝试使用VPN</li>
              <li>使用离线模式</li>
            </ul>
          </div>
          
          <div v-if="!checks.cesium" class="solution-item error">
            <h4>Cesium 模块加载失败</h4>
            <p>Cesium库无法正确加载。</p>
            <ul>
              <li>检查package.json中的cesium依赖</li>
              <li>重新安装依赖: npm install</li>
              <li>清除缓存: npm run dev --force</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 测试容器 -->
    <div ref="testContainer" class="test-container" v-show="showTestContainer">
      <div class="container-label">Cesium 测试容器</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 响应式数据
const testing = ref(false)
const cesiumLoaded = ref(false)
const showTestContainer = ref(false)
const testContainer = ref<HTMLDivElement | null>(null)

const checks = ref({
  browser: false,
  webgl: false,
  network: false,
  cesium: false
})

const browserInfo = ref('')
const webglInfo = ref('')
const networkInfo = ref('')
const cesiumInfo = ref('')

const logs = ref<Array<{type: string, message: string, time: string}>>([])

// 方法
const addLog = (type: 'info' | 'success' | 'warning' | 'error', message: string) => {
  logs.value.unshift({
    type,
    message,
    time: new Date().toLocaleTimeString()
  })
  
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

const checkBrowser = () => {
  const userAgent = navigator.userAgent
  const isChrome = userAgent.includes('Chrome')
  const isFirefox = userAgent.includes('Firefox')
  const isEdge = userAgent.includes('Edge')
  const isSafari = userAgent.includes('Safari') && !userAgent.includes('Chrome')
  
  if (isChrome || isFirefox || isEdge) {
    checks.value.browser = true
    browserInfo.value = isChrome ? 'Chrome' : isFirefox ? 'Firefox' : 'Edge'
    addLog('success', `浏览器检查通过: ${browserInfo.value}`)
  } else {
    checks.value.browser = false
    browserInfo.value = isSafari ? 'Safari (可能不兼容)' : '未知浏览器'
    addLog('warning', `浏览器可能不兼容: ${browserInfo.value}`)
  }
}

const checkWebGL = () => {
  try {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    
    if (gl) {
      checks.value.webgl = true
      const renderer = gl.getParameter(gl.RENDERER)
      webglInfo.value = `支持 - ${renderer}`
      addLog('success', `WebGL检查通过: ${renderer}`)
    } else {
      checks.value.webgl = false
      webglInfo.value = '不支持'
      addLog('error', 'WebGL不支持，Cesium无法运行')
    }
  } catch (error) {
    checks.value.webgl = false
    webglInfo.value = '检查失败'
    addLog('error', `WebGL检查失败: ${error}`)
  }
}

const checkNetwork = async () => {
  try {
    // 测试访问Cesium Ion
    const response = await fetch('https://api.cesium.com/v1/assets', { 
      method: 'HEAD',
      mode: 'no-cors'
    })
    checks.value.network = true
    networkInfo.value = '正常'
    addLog('success', '网络连接正常')
  } catch (error) {
    checks.value.network = false
    networkInfo.value = '连接失败'
    addLog('warning', `网络连接问题: ${error}`)
  }
}

const testCesiumImport = async () => {
  testing.value = true
  addLog('info', '开始测试Cesium模块导入...')
  
  try {
    const Cesium = await import('cesium')
    checks.value.cesium = true
    cesiumLoaded.value = true
    cesiumInfo.value = `版本 ${Cesium.VERSION || '未知'}`
    addLog('success', `Cesium模块加载成功: ${cesiumInfo.value}`)
    
    // 测试基本类
    addLog('info', `Viewer类型: ${typeof Cesium.Viewer}`)
    addLog('info', `Cartesian3类型: ${typeof Cesium.Cartesian3}`)
    addLog('info', `Color类型: ${typeof Cesium.Color}`)
    
  } catch (error: any) {
    checks.value.cesium = false
    cesiumInfo.value = '加载失败'
    addLog('error', `Cesium模块加载失败: ${error.message}`)
  } finally {
    testing.value = false
  }
}

const testBasicViewer = async () => {
  if (!cesiumLoaded.value) {
    addLog('error', '请先测试Cesium导入')
    return
  }
  
  testing.value = true
  showTestContainer.value = true
  addLog('info', '开始测试基础Viewer创建...')
  
  try {
    const Cesium = await import('cesium')
    
    if (!testContainer.value) {
      throw new Error('测试容器未找到')
    }
    
    // 创建最简单的Viewer
    const viewer = new Cesium.Viewer(testContainer.value, {
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false
    })
    
    addLog('success', 'Cesium Viewer创建成功！')
    
    // 5秒后清理
    setTimeout(() => {
      viewer.destroy()
      showTestContainer.value = false
      addLog('info', 'Viewer已清理')
    }, 5000)
    
  } catch (error: any) {
    addLog('error', `Viewer创建失败: ${error.message}`)
    showTestContainer.value = false
  } finally {
    testing.value = false
  }
}

const testWithoutToken = async () => {
  testing.value = true
  addLog('info', '测试无Token模式...')
  
  try {
    const Cesium = await import('cesium')
    
    // 清除Token
    Cesium.Ion.defaultAccessToken = undefined
    
    addLog('success', '无Token模式测试完成')
    
  } catch (error: any) {
    addLog('error', `无Token测试失败: ${error.message}`)
  } finally {
    testing.value = false
  }
}

const useFallbackMode = () => {
  addLog('info', '切换到2D回退模式')
  // 发射事件给父组件
  window.dispatchEvent(new CustomEvent('use-fallback-mode'))
}

// 生命周期
onMounted(() => {
  addLog('info', 'Cesium诊断工具启动')
  
  // 执行基础检查
  checkBrowser()
  checkWebGL()
  checkNetwork()
  
  addLog('info', '基础检查完成，请点击按钮进行功能测试')
})
</script>

<style scoped>
.diagnostic-container {
  display: flex;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #1e3a8a 0%, #7c3aed 100%);
  font-family: 'Microsoft YaHei', sans-serif;
}

.diagnostic-panel {
  width: 60%;
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  overflow-y: auto;
}

.diagnostic-panel h2 {
  color: #1f2937;
  margin: 0 0 20px 0;
  font-size: 24px;
}

.test-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.test-section h3 {
  color: #374151;
  margin: 0 0 12px 0;
  font-size: 16px;
}

.test-results {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 6px;
}

.test-item.success { background: #d1fae5; }
.test-item.warning { background: #fef3c7; }
.test-item.error { background: #fef2f2; }

.test-icon {
  font-size: 16px;
  width: 20px;
}

.test-text {
  font-weight: 500;
  color: #374151;
  min-width: 120px;
}

.test-detail {
  font-size: 12px;
  color: #6b7280;
}

.test-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.test-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: #3b82f6;
  color: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.test-btn:hover:not(:disabled) {
  background: #2563eb;
}

.test-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.test-btn.fallback {
  background: #10b981;
}

.test-btn.fallback:hover {
  background: #059669;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: #1f2937;
  border-radius: 6px;
  padding: 12px;
}

.log-entry {
  font-size: 12px;
  margin-bottom: 4px;
  font-family: 'Courier New', monospace;
}

.log-entry.success { color: #10b981; }
.log-entry.warning { color: #f59e0b; }
.log-entry.error { color: #ef4444; }
.log-entry.info { color: #3b82f6; }

.log-time {
  color: #6b7280;
  margin-right: 8px;
}

.solutions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.solution-item {
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid;
}

.solution-item.error {
  background: #fef2f2;
  border-left-color: #ef4444;
}

.solution-item.warning {
  background: #fef3c7;
  border-left-color: #f59e0b;
}

.solution-item h4 {
  color: #374151;
  margin: 0 0 8px 0;
  font-size: 14px;
}

.solution-item p {
  color: #6b7280;
  margin: 0 0 8px 0;
  font-size: 12px;
}

.solution-item ul {
  margin: 0;
  padding-left: 16px;
}

.solution-item li {
  color: #6b7280;
  font-size: 12px;
  margin-bottom: 4px;
}

.test-container {
  width: 40%;
  position: relative;
  background: #000;
}

.container-label {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(255, 255, 255, 0.9);
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 100;
}

/* 滚动条样式 */
.diagnostic-panel::-webkit-scrollbar,
.log-container::-webkit-scrollbar {
  width: 6px;
}

.diagnostic-panel::-webkit-scrollbar-track,
.log-container::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.diagnostic-panel::-webkit-scrollbar-thumb,
.log-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}
</style>
