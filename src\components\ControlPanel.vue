<script setup lang="ts">
import { ref } from 'vue'

interface Warning {
  id: number
  type: string
  level: number
  location: string
  time: string
}

const warnings = ref<Warning[]>([
  {
    id: 1,
    type: '暴雨预警',
    level: 3,
    location: '北京市海淀区',
    time: '2024-01-08 14:30'
  },
  {
    id: 2,
    type: '狂风预警',
    level: 2,
    location: '北京市朝阳区',
    time: '2024-01-08 14:35'
  }
])

const activeWarnings = ref<Set<string>>(new Set(['暴雨预警', '狂风预警']))

const toggleWarning = (type: string) => {
  if (activeWarnings.value.has(type)) {
    activeWarnings.value.delete(type)
  } else {
    activeWarnings.value.add(type)
  }
  emit('warningToggle', type, activeWarnings.value.has(type))
}

const getWarningColor = (type: string): string => {
  switch (type) {
    case '暴雨预警':
      return 'bg-warning-blue'
    case '狂风预警':
      return 'bg-warning-yellow'
    case '强风预警':
      return 'bg-warning-orange'
    default:
      return 'bg-warning-red'
  }
}

const emit = defineEmits<{
  (e: 'warningToggle', type: string, active: boolean): void
}>()
</script>

<template>
  <aside class="fixed left-0 top-0 h-full w-64 bg-gray-800 text-white p-4 overflow-y-auto" role="complementary">
    <h2 class="text-xl font-bold mb-4">气象预警系统</h2>
    
    <!-- 预警类型切换 -->
    <div class="mb-6" role="group" aria-label="预警类型控制">
      <h3 class="text-lg font-semibold mb-2">预警类型</h3>
      <div class="space-y-2">
        <button
          v-for="warning in warnings"
          :key="warning.id"
          :class="[
            'w-full px-4 py-2 rounded transition-colors',
            getWarningColor(warning.type),
            activeWarnings.has(warning.type) ? 'opacity-100' : 'opacity-50'
          ]"
          @click="toggleWarning(warning.type)"
          :aria-pressed="activeWarnings.has(warning.type)"
        >
          {{ warning.type }}
        </button>
      </div>
    </div>

    <!-- 预警列表 -->
    <section role="region" aria-label="当前预警列表">
      <h3 class="text-lg font-semibold mb-2">当前预警</h3>
      <div class="space-y-4">
        <article
          v-for="warning in warnings"
          :key="warning.id"
          class="bg-gray-700 rounded p-3"
          role="article"
        >
          <div class="flex items-center justify-between">
            <span :class="getWarningColor(warning.type).replace('bg-', 'text-')">
              {{ warning.type }}
            </span>
            <span class="text-sm">等级 {{ warning.level }}</span>
          </div>
          <div class="text-sm text-gray-300 mt-1">{{ warning.location }}</div>
          <div class="text-sm text-gray-400 mt-1">{{ warning.time }}</div>
        </article>
      </div>
    </section>
  </aside>
</template>

<style scoped>
.bg-warning-blue {
  background-color: var(--warning-blue, #33b5e5);
}
.bg-warning-yellow {
  background-color: var(--warning-yellow, #ffbb33);
}
.bg-warning-orange {
  background-color: var(--warning-orange, #ff8800);
}
.bg-warning-red {
  background-color: var(--warning-red, #ff4444);
}

.text-warning-blue {
  color: var(--warning-blue, #33b5e5);
}
.text-warning-yellow {
  color: var(--warning-yellow, #ffbb33);
}
.text-warning-orange {
  color: var(--warning-orange, #ff8800);
}
.text-warning-red {
  color: var(--warning-red, #ff4444);
}
</style> 