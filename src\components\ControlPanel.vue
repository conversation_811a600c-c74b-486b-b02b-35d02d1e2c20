<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { WeatherWarningType } from '@/types/weather'
import type {
  WeatherWarningArea,
  MonitoringStation,
  ControlPanelState
} from '@/types/weather'
import { defaultWeatherEffects, warningLevelColors } from '@/config/weatherConfig'

// Props
interface Props {
  warnings: WeatherWarningArea[]
  stations: MonitoringStation[]
  controlState: ControlPanelState
  selectedStation?: MonitoringStation | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  controlStateUpdate: [state: ControlPanelState]
  stationSelect: [station: MonitoringStation | null]
}>()

// 本地状态
const localControlState = ref<ControlPanelState>({ ...props.controlState })
const currentTime = ref(new Date())

// 预警类型配置
const warningTypes = [
  {
    type: WeatherWarningType.HEAVY_RAIN,
    name: '暴雨预警',
    color: defaultWeatherEffects[WeatherWarningType.HEAVY_RAIN].color
  },
  {
    type: WeatherWarningType.STRONG_WIND,
    name: '狂风预警',
    color: defaultWeatherEffects[WeatherWarningType.STRONG_WIND].color
  },
  {
    type: WeatherWarningType.GALE,
    name: '强风预警',
    color: defaultWeatherEffects[WeatherWarningType.GALE].color
  },
  {
    type: WeatherWarningType.TORRENTIAL_RAIN,
    name: '强降雨预警',
    color: defaultWeatherEffects[WeatherWarningType.TORRENTIAL_RAIN].color
  }
]

// 计算属性
const activeWarningsCount = computed(() => {
  return props.warnings.filter(warning =>
    localControlState.value.activeWarnings.has(warning.type)
  ).length
})

const totalStations = computed(() => props.stations.length)

// 方法
const toggleWarning = (type: WeatherWarningType) => {
  const newActiveWarnings = new Set(localControlState.value.activeWarnings)
  if (newActiveWarnings.has(type)) {
    newActiveWarnings.delete(type)
  } else {
    newActiveWarnings.add(type)
  }
  localControlState.value.activeWarnings = newActiveWarnings
  updateControlState()
}

const isWarningActive = (type: WeatherWarningType) => {
  return localControlState.value.activeWarnings.has(type)
}

const updateControlState = () => {
  emit('controlStateUpdate', { ...localControlState.value })
}

const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 监听props变化
watch(() => props.controlState, (newState) => {
  localControlState.value = { ...newState }
}, { deep: true })

// 时间更新
setInterval(() => {
  currentTime.value = new Date()
}, 1000)
</script>

<template>
  <aside class="fixed left-0 top-0 h-full w-80 bg-gray-900/95 backdrop-blur-sm text-white p-6 overflow-y-auto shadow-2xl z-50" role="complementary">
    <!-- 标题 -->
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-blue-400 mb-2">气象预警控制台</h2>
      <div class="text-sm text-gray-300">
        {{ formatTime(currentTime) }}
      </div>
    </div>

    <!-- 预警类型控制 -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-4 text-green-400">预警类型</h3>
      <div class="space-y-3">
        <div
          v-for="warningType in warningTypes"
          :key="warningType.type"
          class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors"
        >
          <div class="flex items-center space-x-3">
            <div
              class="w-4 h-4 rounded-full"
              :style="{ backgroundColor: warningType.color }"
            ></div>
            <span class="text-sm font-medium">{{ warningType.name }}</span>
          </div>
          <button
            @click="toggleWarning(warningType.type)"
            :class="[
              'px-3 py-1 rounded-md text-xs font-medium transition-colors',
              isWarningActive(warningType.type)
                ? 'bg-blue-600 text-white'
                : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
            ]"
          >
            {{ isWarningActive(warningType.type) ? '开启' : '关闭' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 显示选项 -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-4 text-green-400">显示选项</h3>
      <div class="space-y-3">
        <label class="flex items-center space-x-3 cursor-pointer">
          <input
            type="checkbox"
            v-model="localControlState.showStations"
            @change="updateControlState"
            class="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
          >
          <span class="text-sm">显示监测站点</span>
        </label>
        <label class="flex items-center space-x-3 cursor-pointer">
          <input
            type="checkbox"
            v-model="localControlState.showWarningAreas"
            @change="updateControlState"
            class="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
          >
          <span class="text-sm">显示预警区域</span>
        </label>
      </div>
    </div>

    <!-- 时间控制 -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-4 text-green-400">时间控制</h3>
      <div class="space-y-3">
        <div>
          <label class="block text-sm text-gray-300 mb-2">时间倍速</label>
          <input
            type="range"
            min="0.1"
            max="5"
            step="0.1"
            v-model="localControlState.timeScale"
            @input="updateControlState"
            class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
          >
          <div class="text-xs text-gray-400 mt-1">{{ localControlState.timeScale }}x</div>
        </div>
      </div>
    </div>

    <!-- 当前选中站点信息 -->
    <div v-if="selectedStation" class="mb-6">
      <h3 class="text-lg font-semibold mb-4 text-green-400">站点信息</h3>
      <div class="bg-gray-800/50 rounded-lg p-4 space-y-2">
        <div class="text-sm">
          <span class="text-gray-400">站点名称：</span>
          <span class="text-white">{{ selectedStation.name }}</span>
        </div>
        <div class="text-sm">
          <span class="text-gray-400">温度：</span>
          <span class="text-white">{{ selectedStation.temperature }}°C</span>
        </div>
        <div class="text-sm">
          <span class="text-gray-400">湿度：</span>
          <span class="text-white">{{ selectedStation.humidity }}%</span>
        </div>
        <div class="text-sm">
          <span class="text-gray-400">风速：</span>
          <span class="text-white">{{ selectedStation.windSpeed }} m/s</span>
        </div>
        <div class="text-sm">
          <span class="text-gray-400">气压：</span>
          <span class="text-white">{{ selectedStation.pressure }} hPa</span>
        </div>
        <div class="text-sm">
          <span class="text-gray-400">能见度：</span>
          <span class="text-white">{{ selectedStation.visibility }} km</span>
        </div>
      </div>
    </div>

    <!-- 预警统计 -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-4 text-green-400">预警统计</h3>
      <div class="grid grid-cols-2 gap-3">
        <div class="bg-blue-600/20 rounded-lg p-3 text-center">
          <div class="text-2xl font-bold text-blue-400">{{ activeWarningsCount }}</div>
          <div class="text-xs text-gray-300">活跃预警</div>
        </div>
        <div class="bg-green-600/20 rounded-lg p-3 text-center">
          <div class="text-2xl font-bold text-green-400">{{ totalStations }}</div>
          <div class="text-xs text-gray-300">监测站点</div>
        </div>
      </div>
    </div>

    <!-- 当前预警列表 -->
    <div v-if="props.warnings.length > 0">
      <h3 class="text-lg font-semibold mb-4 text-green-400">当前预警</h3>
      <div class="space-y-3">
        <div
          v-for="warning in props.warnings"
          :key="warning.id"
          class="bg-gray-800/50 rounded-lg p-3 hover:bg-gray-700/50 transition-colors"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-2">
              <div
                class="w-3 h-3 rounded-full"
                :style="{ backgroundColor: defaultWeatherEffects[warning.type].color }"
              ></div>
              <span class="text-sm font-medium">{{ warningTypes.find(t => t.type === warning.type)?.name }}</span>
            </div>
            <span
              class="text-xs px-2 py-1 rounded"
              :style="{ backgroundColor: warningLevelColors[warning.level] }"
            >
              {{ warning.level.toUpperCase() }}
            </span>
          </div>
          <div class="text-xs text-gray-400">
            强度: {{ Math.round(warning.intensity * 100) }}%
          </div>
          <div class="text-xs text-gray-400">
            半径: {{ warning.radius }}m
          </div>
        </div>
      </div>
    </div>
  </aside>
</template>

<style scoped>
/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* 自定义范围滑块 */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #1e40af;
}

input[type="range"]::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #1e40af;
}

/* 动画效果 */
.transition-colors {
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  aside {
    width: 100%;
    height: auto;
    position: relative;
  }
}
</style>