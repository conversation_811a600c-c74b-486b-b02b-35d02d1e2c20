// 测试工具函数
import { WeatherWarningType, WarningLevel } from '@/types/weather'
import type { WeatherWarningArea, MonitoringStation } from '@/types/weather'

// 测试数据生成
export const createTestStation = (id: string): MonitoringStation => ({
  id,
  name: `测试站点${id}`,
  position: { x: 0, y: 0, z: 0 },
  altitude: 100,
  temperature: 20,
  humidity: 60,
  windSpeed: 5,
  windDirection: 180,
  pressure: 1013,
  visibility: 10,
  lastUpdate: new Date()
})

export const createTestWarning = (id: string): WeatherWarningArea => ({
  id,
  type: WeatherWarningType.HEAVY_RAIN,
  level: WarningLevel.YELLOW,
  center: { x: 0, y: 0, z: 0 },
  radius: 50,
  intensity: 0.8,
  startTime: new Date(),
  endTime: new Date(Date.now() + 3600000),
  description: '测试预警',
  affectedStations: []
})

// 验证函数
export const validateStation = (station: MonitoringStation): boolean => {
  return !!(
    station.id &&
    station.name &&
    station.position &&
    typeof station.temperature === 'number' &&
    typeof station.humidity === 'number'
  )
}

export const validateWarning = (warning: WeatherWarningArea): boolean => {
  return !!(
    warning.id &&
    warning.type &&
    warning.level &&
    warning.center &&
    typeof warning.radius === 'number' &&
    typeof warning.intensity === 'number'
  )
}

// 控制台测试函数
export const runBasicTests = (): void => {
  console.log('🧪 开始基础测试...')
  
  // 测试站点创建
  const testStation = createTestStation('test-1')
  const stationValid = validateStation(testStation)
  console.log(`✅ 站点创建测试: ${stationValid ? '通过' : '失败'}`)
  
  // 测试预警创建
  const testWarning = createTestWarning('warning-1')
  const warningValid = validateWarning(testWarning)
  console.log(`✅ 预警创建测试: ${warningValid ? '通过' : '失败'}`)
  
  // 测试枚举值
  const warningTypes = Object.values(WeatherWarningType)
  const warningLevels = Object.values(WarningLevel)
  console.log(`✅ 预警类型数量: ${warningTypes.length}`)
  console.log(`✅ 预警级别数量: ${warningLevels.length}`)
  
  console.log('🎉 基础测试完成!')
}

// 性能测试
export const runPerformanceTest = (): void => {
  console.log('⚡ 开始性能测试...')
  
  const startTime = performance.now()
  
  // 创建大量测试数据
  const stations: MonitoringStation[] = []
  const warnings: WeatherWarningArea[] = []
  
  for (let i = 0; i < 100; i++) {
    stations.push(createTestStation(`perf-station-${i}`))
  }
  
  for (let i = 0; i < 50; i++) {
    warnings.push(createTestWarning(`perf-warning-${i}`))
  }
  
  const endTime = performance.now()
  const duration = endTime - startTime
  
  console.log(`✅ 创建100个站点和50个预警用时: ${duration.toFixed(2)}ms`)
  console.log(`✅ 平均每个对象创建时间: ${(duration / 150).toFixed(2)}ms`)
  
  console.log('🎉 性能测试完成!')
}

// 在开发环境下自动运行测试
if (import.meta.env.DEV) {
  // 延迟执行，确保所有模块都已加载
  setTimeout(() => {
    runBasicTests()
    runPerformanceTest()
  }, 1000)
}
