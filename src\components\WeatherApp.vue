<template>
  <div class="weather-app">
    <!-- 全屏Cesium地球 -->
    <FullscreenCesium 
      ref="cesiumRef"
      @entity-select="handleEntitySelect"
    />
    
    <!-- 气象控制面板 -->
    <WeatherControlPanel
      :warnings="warnings"
      :stations="stations"
      :active-warning-types="activeWarningTypes"
      @toggle-warning-type="handleToggleWarningType"
      @focus-zhuhai="handleFocusZhuhai"
      @zoom-in="handleZoomIn"
      @zoom-out="handleZoomOut"
      @reset-view="handleResetView"
      @toggle-stations="handleToggleStations"
      @toggle-warnings="handleToggleWarnings"
      @toggle-boundaries="handleToggleBoundaries"
      @emergency-mode="handleEmergencyMode"
      @refresh-data="handleRefreshData"
      @export-data="handleExportData"
    />
    
    <!-- 气象数据面板 -->
    <WeatherDataPanel
      :warnings="warnings"
      :stations="stations"
      @station-select="handleStationSelect"
    />
    
    <!-- 紧急模式覆盖层 -->
    <div v-if="emergencyMode" class="emergency-overlay">
      <div class="emergency-content">
        <div class="emergency-header">
          <h2>🚨 紧急模式已激活</h2>
          <button @click="emergencyMode = false" class="close-emergency">✕</button>
        </div>
        <div class="emergency-warnings">
          <div 
            v-for="warning in criticalWarnings" 
            :key="warning.id"
            class="critical-warning"
          >
            <div class="warning-icon">{{ getWarningIcon(warning.type) }}</div>
            <div class="warning-content">
              <h3>{{ getWarningTypeName(warning.type) }} - {{ getWarningLevelName(warning.level) }}</h3>
              <p>{{ warning.description }}</p>
              <div class="affected-areas">
                影响区域: {{ warning.affectedAreas.join(', ') }}
              </div>
            </div>
          </div>
        </div>
        <div class="emergency-actions">
          <button @click="handleEmergencyResponse" class="emergency-btn primary">
            启动应急响应
          </button>
          <button @click="handleNotifyAuthorities" class="emergency-btn secondary">
            通知相关部门
          </button>
          <button @click="handleEvacuationPlan" class="emergency-btn warning">
            疏散计划
          </button>
        </div>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p>正在加载珠海市气象预警系统...</p>
      </div>
    </div>
    
    <!-- 通知消息 -->
    <div v-if="notification" class="notification" :class="notification.type">
      <div class="notification-content">
        <span class="notification-icon">{{ getNotificationIcon(notification.type) }}</span>
        <span class="notification-message">{{ notification.message }}</span>
        <button @click="notification = null" class="notification-close">✕</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import FullscreenCesium from './FullscreenCesium.vue'
import WeatherControlPanel from './WeatherControlPanel.vue'
import WeatherDataPanel from './WeatherDataPanel.vue'
import { weatherWarningService } from '@/services/weatherWarningService'
import { WeatherWarningType, WarningLevel } from '@/types/weather'
import type { WeatherWarningArea, MonitoringStation } from '@/types/weather'

// 响应式数据
const cesiumRef = ref()
const loading = ref(true)
const emergencyMode = ref(false)
const notification = ref<{type: 'success' | 'warning' | 'error' | 'info', message: string} | null>(null)

const warnings = ref<WeatherWarningArea[]>([])
const stations = ref<MonitoringStation[]>([])
const activeWarningTypes = ref<Set<WeatherWarningType>>(new Set([
  WeatherWarningType.HEAVY_RAIN,
  WeatherWarningType.STRONG_WIND
]))

// 计算属性
const criticalWarnings = computed(() => 
  warnings.value.filter(w => 
    w.isActive && 
    (w.level === WarningLevel.ORANGE || w.level === WarningLevel.RED)
  )
)

// 事件处理方法
const handleEntitySelect = (entity: any) => {
  console.log('选中实体:', entity)
  if (entity.customData?.type === '监测站点') {
    showNotification('info', `已选择监测站点: ${entity.customData.name}`)
  } else if (entity.customData?.type === '预警区域') {
    showNotification('warning', `预警区域: ${entity.customData.name}`)
  }
}

const handleToggleWarningType = (type: WeatherWarningType) => {
  if (activeWarningTypes.value.has(type)) {
    activeWarningTypes.value.delete(type)
  } else {
    activeWarningTypes.value.add(type)
  }
  // 触发Cesium更新
  if (cesiumRef.value) {
    cesiumRef.value.updateWarningDisplay?.(activeWarningTypes.value)
  }
}

const handleFocusZhuhai = () => {
  cesiumRef.value?.focusOnZhuhai?.()
  showNotification('info', '已定位到珠海市中心')
}

const handleZoomIn = () => {
  cesiumRef.value?.zoomIn?.()
}

const handleZoomOut = () => {
  cesiumRef.value?.zoomOut?.()
}

const handleResetView = () => {
  cesiumRef.value?.focusOnZhuhai?.()
  showNotification('info', '视角已重置')
}

const handleToggleStations = (show: boolean) => {
  cesiumRef.value?.toggleStations?.(show)
  showNotification('info', `监测站点${show ? '已显示' : '已隐藏'}`)
}

const handleToggleWarnings = (show: boolean) => {
  cesiumRef.value?.toggleWarnings?.(show)
  showNotification('info', `预警区域${show ? '已显示' : '已隐藏'}`)
}

const handleToggleBoundaries = (show: boolean) => {
  cesiumRef.value?.toggleBoundaries?.(show)
  showNotification('info', `行政边界${show ? '已显示' : '已隐藏'}`)
}

const handleEmergencyMode = () => {
  emergencyMode.value = true
  showNotification('error', '紧急模式已激活！')
}

const handleRefreshData = () => {
  // 刷新数据
  stations.value = weatherWarningService.getStations()
  warnings.value = weatherWarningService.getWarnings()
  showNotification('success', '数据已刷新')
}

const handleExportData = () => {
  // 导出数据逻辑
  const data = {
    warnings: warnings.value,
    stations: stations.value,
    timestamp: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `weather-data-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  showNotification('success', '数据已导出')
}

const handleStationSelect = (station: MonitoringStation) => {
  cesiumRef.value?.flyToStation?.(station)
  showNotification('info', `已定位到${station.name}`)
}

// 紧急模式处理
const handleEmergencyResponse = () => {
  showNotification('warning', '应急响应程序已启动')
}

const handleNotifyAuthorities = () => {
  showNotification('info', '已通知相关部门')
}

const handleEvacuationPlan = () => {
  showNotification('warning', '疏散计划已激活')
}

// 工具方法
const showNotification = (type: 'success' | 'warning' | 'error' | 'info', message: string) => {
  notification.value = { type, message }
  setTimeout(() => {
    notification.value = null
  }, 3000)
}

const getWarningIcon = (type: WeatherWarningType) => {
  const icons = {
    [WeatherWarningType.HEAVY_RAIN]: '🌧️',
    [WeatherWarningType.STRONG_WIND]: '💨',
    [WeatherWarningType.GALE]: '🌪️',
    [WeatherWarningType.TORRENTIAL_RAIN]: '⛈️'
  }
  return icons[type] || '⚠️'
}

const getWarningTypeName = (type: WeatherWarningType) => {
  const names = {
    [WeatherWarningType.HEAVY_RAIN]: '暴雨预警',
    [WeatherWarningType.STRONG_WIND]: '大风预警',
    [WeatherWarningType.GALE]: '强风预警',
    [WeatherWarningType.TORRENTIAL_RAIN]: '强降雨预警'
  }
  return names[type] || '未知预警'
}

const getWarningLevelName = (level: WarningLevel) => {
  const names = {
    [WarningLevel.BLUE]: '蓝色',
    [WarningLevel.YELLOW]: '黄色',
    [WarningLevel.ORANGE]: '橙色',
    [WarningLevel.RED]: '红色'
  }
  return names[level] || '未知'
}

const getNotificationIcon = (type: string) => {
  const icons = {
    success: '✅',
    warning: '⚠️',
    error: '❌',
    info: 'ℹ️'
  }
  return icons[type] || 'ℹ️'
}

// 生命周期
onMounted(async () => {
  try {
    // 初始化数据
    stations.value = weatherWarningService.getStations()
    warnings.value = weatherWarningService.getWarnings()
    
    // 启动实时数据更新
    weatherWarningService.startRealTimeUpdates()
    
    // 监听数据更新
    weatherWarningService.onUpdate((newWarnings, newStations) => {
      warnings.value = newWarnings
      stations.value = newStations
    })
    
    // 延迟隐藏加载状态
    setTimeout(() => {
      loading.value = false
      showNotification('success', '珠海市气象预警系统已启动')
    }, 2000)
    
  } catch (error) {
    console.error('系统初始化失败:', error)
    loading.value = false
    showNotification('error', '系统初始化失败')
  }
})
</script>

<style scoped>
.weather-app {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  font-family: 'Microsoft YaHei', sans-serif;
}

/* 紧急模式覆盖层 */
.emergency-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(239, 68, 68, 0.95);
  backdrop-filter: blur(10px);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: emergencyPulse 2s infinite;
}

.emergency-content {
  background: white;
  border-radius: 16px;
  padding: 32px;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.emergency-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #fee2e2;
}

.emergency-header h2 {
  color: #dc2626;
  font-size: 24px;
  margin: 0;
}

.close-emergency {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
}

.emergency-warnings {
  margin-bottom: 24px;
}

.critical-warning {
  display: flex;
  gap: 16px;
  padding: 16px;
  background: #fef2f2;
  border-radius: 12px;
  border-left: 4px solid #ef4444;
  margin-bottom: 12px;
}

.warning-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.warning-content h3 {
  color: #dc2626;
  margin: 0 0 8px 0;
  font-size: 16px;
}

.warning-content p {
  color: #374151;
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.affected-areas {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.emergency-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.emergency-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.emergency-btn.primary {
  background: #dc2626;
  color: white;
}

.emergency-btn.secondary {
  background: #3b82f6;
  color: white;
}

.emergency-btn.warning {
  background: #f59e0b;
  color: white;
}

.emergency-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 加载覆盖层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1e3a8a 0%, #7c3aed 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.loading-content p {
  font-size: 18px;
  margin: 0;
}

/* 通知消息 */
.notification {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2500;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  animation: slideIn 0.3s ease;
}

.notification.success { background: #d1fae5; border: 1px solid #10b981; }
.notification.warning { background: #fef3c7; border: 1px solid #f59e0b; }
.notification.error { background: #fef2f2; border: 1px solid #ef4444; }
.notification.info { background: #dbeafe; border: 1px solid #3b82f6; }

.notification-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
}

.notification-icon {
  font-size: 20px;
}

.notification-message {
  font-weight: 500;
  color: #374151;
}

.notification-close {
  background: none;
  border: none;
  font-size: 16px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  margin-left: 8px;
}

/* 动画 */
@keyframes emergencyPulse {
  0%, 100% { background: rgba(239, 68, 68, 0.95); }
  50% { background: rgba(239, 68, 68, 0.85); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -60%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .emergency-content {
    margin: 20px;
    padding: 24px;
    max-width: none;
  }
  
  .emergency-actions {
    flex-direction: column;
  }
  
  .emergency-btn {
    width: 100%;
  }
}
</style>
