# 故障排除指南

## 常见问题及解决方案

### 1. Vue Inspector 插件错误

**错误信息：**
```
[plugin:vite-plugin-vue-inspector] Invalid end tag.
```

**原因：**
- Vue文件中存在语法错误
- 标签未正确闭合
- script标签结构不正确

**解决方案：**
1. 检查Vue文件中的标签是否正确闭合
2. 确保`<script>`、`<template>`、`<style>`标签结构正确
3. 如果问题持续，可以临时禁用vue-devtools插件：

```typescript
// vite.config.ts
export default defineConfig({
  plugins: [
    vue(),
    // vueDevTools(), // 注释掉这行
  ],
})
```

### 2. Three.js 相关错误

**错误信息：**
```
Cannot read properties of undefined (reading 'domElement')
```

**解决方案：**
1. 确保在组件挂载后再初始化Three.js场景
2. 检查DOM元素是否正确获取
3. 添加空值检查：

```typescript
if (container.value) {
  container.value.appendChild(renderer.domElement)
}
```

### 3. TypeScript 类型错误

**错误信息：**
```
Property 'xxx' does not exist on type 'xxx'
```

**解决方案：**
1. 检查类型定义是否正确导入
2. 确保接口定义完整
3. 使用类型断言（谨慎使用）：

```typescript
const element = document.getElementById('app') as HTMLElement
```

### 4. 模块导入错误

**错误信息：**
```
Cannot resolve module '@/xxx'
```

**解决方案：**
1. 检查vite.config.ts中的路径别名配置：

```typescript
resolve: {
  alias: {
    '@': fileURLToPath(new URL('./src', import.meta.url))
  }
}
```

2. 确保文件路径正确
3. 重启开发服务器

### 5. 性能问题

**症状：**
- 页面卡顿
- 内存占用过高
- 渲染缓慢

**解决方案：**
1. 减少粒子数量
2. 优化材质和纹理
3. 使用LOD（细节层次）技术
4. 及时清理不用的几何体和材质：

```typescript
geometry.dispose()
material.dispose()
texture.dispose()
```

### 6. 浏览器兼容性问题

**症状：**
- 某些浏览器无法正常显示
- WebGL错误

**解决方案：**
1. 检查浏览器WebGL支持：

```javascript
const canvas = document.createElement('canvas')
const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
if (!gl) {
  console.error('WebGL not supported')
}
```

2. 提供降级方案
3. 更新浏览器版本

### 7. 开发服务器问题

**症状：**
- 热重载不工作
- 文件变更不生效

**解决方案：**
1. 重启开发服务器：
```bash
npm run dev
```

2. 清除缓存：
```bash
rm -rf node_modules/.vite
npm run dev
```

3. 检查文件监听限制（Linux/Mac）：
```bash
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 8. 构建错误

**错误信息：**
```
Build failed with errors
```

**解决方案：**
1. 检查TypeScript类型错误：
```bash
npm run type-check
```

2. 检查ESLint错误：
```bash
npm run lint
```

3. 清理并重新构建：
```bash
rm -rf dist
npm run build
```

## 调试技巧

### 1. 使用浏览器开发者工具
- F12 打开开发者工具
- 查看Console面板的错误信息
- 使用Network面板检查资源加载
- 使用Performance面板分析性能

### 2. Vue DevTools
- 安装Vue DevTools浏览器扩展
- 检查组件状态和props
- 监控事件和数据流

### 3. Three.js 调试
- 使用Three.js Inspector扩展
- 添加辅助对象（AxesHelper, GridHelper）
- 使用stats.js监控性能

### 4. 日志调试
```typescript
// 添加详细日志
console.log('Scene initialized:', scene)
console.log('Camera position:', camera.position)
console.log('Renderer info:', renderer.info)
```

## 获取帮助

如果以上解决方案都无法解决问题：

1. 检查项目的GitHub Issues
2. 查看Vue.js官方文档
3. 查看Three.js官方文档
4. 在Stack Overflow搜索相关问题
5. 联系技术支持团队

## 预防措施

1. **定期更新依赖**：
```bash
npm update
```

2. **使用TypeScript严格模式**：
```json
{
  "compilerOptions": {
    "strict": true
  }
}
```

3. **编写单元测试**
4. **使用ESLint和Prettier**
5. **定期备份代码**
