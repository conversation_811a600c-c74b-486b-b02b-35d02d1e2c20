<template>
  <div class="fallback-container">
    <!-- 背景地图 -->
    <div class="map-background">
      <div class="map-overlay">
        <!-- 珠海市地图区域 -->
        <div class="zhuhai-map">
          <div class="city-center" @click="selectLocation('珠海市中心')">
            <div class="location-marker main-city">
              <span class="marker-icon">🏛️</span>
              <span class="marker-label">珠海市中心</span>
            </div>
          </div>
          
          <!-- 香洲区 -->
          <div class="district xiangzhou">
            <h3 class="district-title">香洲区</h3>
            <div class="station-marker" @click="selectStation('香洲监测站')">
              <span class="station-icon">📍</span>
              <span class="station-name">香洲监测站</span>
              <div class="station-data">
                <span>28.5°C</span>
                <span>75%</span>
                <span>12.3m/s</span>
              </div>
            </div>
            <div class="station-marker" @click="selectStation('吉大监测站')">
              <span class="station-icon">📍</span>
              <span class="station-name">吉大监测站</span>
              <div class="station-data">
                <span>27.8°C</span>
                <span>78%</span>
                <span>15.6m/s</span>
              </div>
            </div>
          </div>
          
          <!-- 金湾区 -->
          <div class="district jinwan">
            <h3 class="district-title">金湾区</h3>
            <div class="station-marker" @click="selectStation('金湾监测站')">
              <span class="station-icon">📍</span>
              <span class="station-name">金湾监测站</span>
              <div class="station-data">
                <span>29.2°C</span>
                <span>72%</span>
                <span>18.9m/s</span>
              </div>
            </div>
            <!-- 预警区域 -->
            <div class="warning-area heavy-rain" title="暴雨黄色预警">
              <span class="warning-icon">🌧️</span>
              <span class="warning-text">暴雨预警</span>
            </div>
          </div>
          
          <!-- 斗门区 -->
          <div class="district doumen">
            <h3 class="district-title">斗门区</h3>
            <div class="station-marker" @click="selectStation('斗门监测站')">
              <span class="station-icon">📍</span>
              <span class="station-name">斗门监测站</span>
              <div class="station-data">
                <span>26.9°C</span>
                <span>80%</span>
                <span>8.7m/s</span>
              </div>
            </div>
            <!-- 预警区域 -->
            <div class="warning-area strong-wind" title="大风橙色预警">
              <span class="warning-icon">💨</span>
              <span class="warning-text">大风预警</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 信息提示 -->
    <div class="info-banner">
      <div class="banner-content">
        <span class="banner-icon">ℹ️</span>
        <span class="banner-text">Cesium 3D地球加载失败，已切换到2D地图模式</span>
        <button @click="retryLoad" class="retry-btn">重试加载3D</button>
      </div>
    </div>
    
    <!-- 选中信息面板 -->
    <div v-if="selectedInfo" class="info-panel">
      <div class="panel-header">
        <h3>{{ selectedInfo.name }}</h3>
        <button @click="selectedInfo = null" class="close-btn">✕</button>
      </div>
      <div class="panel-content">
        <div v-if="selectedInfo.type === 'station'" class="station-details">
          <div class="data-row">
            <span class="data-label">温度:</span>
            <span class="data-value">{{ selectedInfo.temperature }}</span>
          </div>
          <div class="data-row">
            <span class="data-label">湿度:</span>
            <span class="data-value">{{ selectedInfo.humidity }}</span>
          </div>
          <div class="data-row">
            <span class="data-label">风速:</span>
            <span class="data-value">{{ selectedInfo.windSpeed }}</span>
          </div>
          <div class="data-row">
            <span class="data-label">状态:</span>
            <span class="data-value status-online">在线</span>
          </div>
        </div>
        <div v-else class="location-details">
          <p>{{ selectedInfo.description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 响应式数据
const selectedInfo = ref<any>(null)

// 方法
const selectStation = (stationName: string) => {
  const stationData = {
    '香洲监测站': { temperature: '28.5°C', humidity: '75%', windSpeed: '12.3m/s' },
    '吉大监测站': { temperature: '27.8°C', humidity: '78%', windSpeed: '15.6m/s' },
    '金湾监测站': { temperature: '29.2°C', humidity: '72%', windSpeed: '18.9m/s' },
    '斗门监测站': { temperature: '26.9°C', humidity: '80%', windSpeed: '8.7m/s' }
  }
  
  selectedInfo.value = {
    type: 'station',
    name: stationName,
    ...stationData[stationName as keyof typeof stationData]
  }
}

const selectLocation = (locationName: string) => {
  selectedInfo.value = {
    type: 'location',
    name: locationName,
    description: '珠海市位于广东省南部，珠江口西岸，是粤港澳大湾区重要城市之一。'
  }
}

const retryLoad = () => {
  // 发出重试事件
  window.location.reload()
}

// 暴露方法给父组件
defineExpose({
  focusOnZhuhai: () => {
    console.log('定位到珠海市中心')
  },
  zoomIn: () => {
    console.log('放大视图')
  },
  zoomOut: () => {
    console.log('缩小视图')
  }
})
</script>

<style scoped>
.fallback-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  font-family: 'Microsoft YaHei', sans-serif;
}

/* 地图背景 */
.map-background {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
  position: relative;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 30% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
}

/* 珠海市地图 */
.zhuhai-map {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 800px;
  height: 600px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

/* 市中心 */
.city-center {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.location-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.location-marker:hover {
  transform: scale(1.1);
}

.main-city .marker-icon {
  font-size: 32px;
  margin-bottom: 8px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.marker-label {
  background: rgba(255, 255, 255, 0.9);
  color: #1f2937;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 行政区 */
.district {
  position: absolute;
  padding: 20px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
}

.xiangzhou {
  top: 20%;
  right: 15%;
  width: 200px;
  background: rgba(34, 197, 94, 0.2);
}

.jinwan {
  bottom: 25%;
  left: 10%;
  width: 180px;
  background: rgba(249, 115, 22, 0.2);
}

.doumen {
  top: 15%;
  left: 15%;
  width: 180px;
  background: rgba(168, 85, 247, 0.2);
}

.district-title {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-align: center;
}

/* 监测站点 */
.station-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 8px 0;
  padding: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.station-marker:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.station-icon {
  font-size: 16px;
  margin-bottom: 4px;
}

.station-name {
  color: white;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
}

.station-data {
  display: flex;
  gap: 6px;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
}

/* 预警区域 */
.warning-area {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  animation: pulse 2s infinite;
}

.heavy-rain {
  top: -10px;
  right: -10px;
  background: rgba(251, 191, 36, 0.8);
  color: #92400e;
}

.strong-wind {
  bottom: -10px;
  left: -10px;
  background: rgba(249, 115, 22, 0.8);
  color: #9a3412;
}

.warning-icon {
  font-size: 14px;
}

/* 信息横幅 */
.info-banner {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
}

.banner-content {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(59, 130, 246, 0.9);
  color: white;
  padding: 12px 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.banner-icon {
  font-size: 18px;
}

.banner-text {
  font-size: 14px;
  font-weight: 500;
}

.retry-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 信息面板 */
.info-panel {
  position: absolute;
  top: 50%;
  right: 30px;
  transform: translateY(-50%);
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20px);
  z-index: 200;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.panel-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
}

.panel-content {
  padding: 16px 20px;
}

.data-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.data-label {
  color: #6b7280;
  font-size: 14px;
}

.data-value {
  color: #1f2937;
  font-size: 14px;
  font-weight: 500;
}

.status-online {
  color: #10b981;
}

.location-details p {
  color: #374151;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

/* 动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .zhuhai-map {
    width: 90vw;
    height: 70vh;
  }
  
  .district {
    padding: 12px;
  }
  
  .info-panel {
    right: 10px;
    left: 10px;
    width: auto;
    top: auto;
    bottom: 20px;
    transform: none;
  }
  
  .banner-content {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}
</style>
