<script setup lang="ts">
import { ref } from 'vue'
import SimpleCesiumFixed from './components/SimpleCesiumFixed.vue'
// 导入测试工具（仅在开发环境）
// if (import.meta.env.DEV) {
//   import('@/utils/testUtils')
//   import('@/utils/debugUtils')
// }

// 简化的应用状态
const appReady = ref(true)

// 简化的应用逻辑
console.log('珠海市气象预警系统启动')
</script>

<template>
  <div class="app">
    <!-- 珠海市气象预警系统 - Cesium版本 -->
    <SimpleCesiumFixed />
  </div>
</template>

<style>
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app {
  width: 100vw;
  height: 100vh;
  position: relative;
}

/* 全局动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app {
    flex-direction: column;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}
</style>
