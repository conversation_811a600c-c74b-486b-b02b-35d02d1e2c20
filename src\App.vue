<script setup lang="ts">
import { ref } from 'vue'
import ThreeScene from './components/ThreeScene.vue'
import ControlPanel from './components/ControlPanel.vue'

const threeScene = ref()

const handleWarningToggle = (type: string, active: boolean) => {
  if (!threeScene.value) return

  switch (type) {
    case '暴雨预警':
      if (active) {
        threeScene.value.createRain()
      }
      break
    case '狂风预警':
      if (active) {
        threeScene.value.createWind()
      }
      break
  }
}
</script>

<template>
  <div class="app">
    <ThreeScene ref="threeScene" />
    <ControlPanel @warning-toggle="handleWarningToggle" />
  </div>
</template>

<style>
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.app {
  width: 100vw;
  height: 100vh;
  position: relative;
}
</style>
