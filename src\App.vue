<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import ThreeScene from './components/ThreeScene.vue'
import ControlPanel from './components/ControlPanel.vue'
import {
  WeatherWarningType,
  WeatherWarningArea,
  MonitoringStation,
  ControlPanelState
} from '@/types/weather'
import { WeatherDataService } from '@/services/weatherService'

// 组件引用
const threeScene = ref()

// 数据状态
const stations = ref<MonitoringStation[]>([])
const warnings = ref<WeatherWarningArea[]>([])
const selectedStation = ref<MonitoringStation | null>(null)

// 控制面板状态
const controlState = ref<ControlPanelState>({
  activeWarnings: new Set([WeatherWarningType.HEAVY_RAIN, WeatherWarningType.STRONG_WIND]),
  selectedStation: null,
  timeScale: 1.0,
  showStations: true,
  showWarningAreas: true,
  currentTime: new Date()
})

// 气象数据服务
const weatherService = new WeatherDataService()

// 事件处理
const handleControlStateUpdate = (newState: ControlPanelState) => {
  controlState.value = { ...newState }
}

const handleStationClick = (station: MonitoringStation) => {
  selectedStation.value = station
  controlState.value.selectedStation = station.id
}

const handleWarningAreaClick = (warning: WeatherWarningArea) => {
  console.log('Warning area clicked:', warning)
}

const handleStationSelect = (station: MonitoringStation | null) => {
  selectedStation.value = station
}

// 数据更新回调
const handleDataUpdate = (newStations: MonitoringStation[], newWarnings: WeatherWarningArea[]) => {
  stations.value = newStations
  warnings.value = newWarnings
  controlState.value.currentTime = new Date()
}
</script>

// 生命周期
onMounted(() => {
  // 初始化数据
  stations.value = weatherService.getStations()
  warnings.value = weatherService.getWarnings()

  // 启动实时数据更新
  weatherService.startRealTimeUpdates(handleDataUpdate)
})

onBeforeUnmount(() => {
  // 停止数据更新
  weatherService.stopRealTimeUpdates()
})
</script>

<template>
  <div class="app">
    <!-- 三维场景 -->
    <ThreeScene
      ref="threeScene"
      :warnings="warnings"
      :stations="stations"
      :control-state="controlState"
      @station-click="handleStationClick"
      @warning-area-click="handleWarningAreaClick"
    />

    <!-- 控制面板 -->
    <ControlPanel
      :warnings="warnings"
      :stations="stations"
      :control-state="controlState"
      :selected-station="selectedStation"
      @control-state-update="handleControlStateUpdate"
      @station-select="handleStationSelect"
    />

    <!-- 加载指示器 -->
    <div
      v-if="stations.length === 0"
      class="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg p-6 text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-700">正在加载气象数据...</p>
      </div>
    </div>
  </div>
</template>

<style>
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app {
  width: 100vw;
  height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
}

/* 全局动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app {
    flex-direction: column;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}
</style>
