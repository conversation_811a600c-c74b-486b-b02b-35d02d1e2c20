<template>
  <div id="cesium-container" class="w-full h-full">
    <!-- 加载指示器 -->
    <div v-if="!isMapLoaded" class="absolute inset-0 bg-gray-900 flex items-center justify-center z-50">
      <div class="text-center text-white">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p class="text-lg">正在加载珠海市气象地图...</p>
        <p class="text-sm text-gray-400 mt-2">请稍候</p>
      </div>
    </div>
    
    <!-- 控制面板 -->
    <div v-if="isMapLoaded" class="absolute top-4 left-4 z-50 bg-black/80 backdrop-blur-sm rounded-lg p-4 text-white min-w-[300px]">
      <h3 class="text-lg font-bold mb-3 text-cyan-400">珠海市气象监测系统</h3>
      
      <!-- 图层控制 -->
      <div class="mb-4">
        <h4 class="text-sm font-semibold mb-2 text-gray-300">图层控制</h4>
        <div class="space-y-2">
          <label class="flex items-center space-x-2 cursor-pointer">
            <input 
              type="checkbox" 
              v-model="layerVisibility.stations"
              @change="toggleStationLayer"
              class="rounded"
            >
            <span class="text-sm">监测站点</span>
          </label>
          <label class="flex items-center space-x-2 cursor-pointer">
            <input 
              type="checkbox" 
              v-model="layerVisibility.warnings"
              @change="toggleWarningLayer"
              class="rounded"
            >
            <span class="text-sm">预警区域</span>
          </label>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="space-y-2">
        <button 
          @click="focusOnZhuhai"
          class="w-full bg-blue-600 hover:bg-blue-700 px-3 py-2 rounded text-sm transition-colors"
        >
          重置视角
        </button>
      </div>
    </div>
    
    <!-- 详情面板 -->
    <div v-if="selectedEntity && isMapLoaded" class="absolute top-4 right-4 z-50 bg-black/80 backdrop-blur-sm rounded-lg p-4 text-white min-w-[250px]">
      <h4 class="text-lg font-bold mb-2 text-cyan-400">详细信息</h4>
      <div class="space-y-2 text-sm">
        <div><strong>名称:</strong> {{ selectedEntity.name }}</div>
        <div><strong>类型:</strong> {{ selectedEntity.type }}</div>
        <div v-if="selectedEntity.location">
          <strong>位置:</strong> {{ selectedEntity.location }}
        </div>
        <div v-if="selectedEntity.level">
          <strong>等级:</strong> {{ selectedEntity.level }}
        </div>
        <div v-if="selectedEntity.description">
          <strong>描述:</strong> {{ selectedEntity.description }}
        </div>
      </div>
      <button 
        @click="selectedEntity = null"
        class="mt-3 w-full bg-gray-600 hover:bg-gray-700 px-3 py-1 rounded text-sm transition-colors"
      >
        关闭
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'

// 珠海市中心坐标
const ZHUHAI_CENTER = {
  longitude: 113.5767,
  latitude: 22.2769,
  height: 50000
}

// 响应式数据
const selectedEntity = ref<any>(null)
const isMapLoaded = ref(false)

// 图层可见性控制
const layerVisibility = reactive({
  stations: true,
  warnings: true
})

// Cesium相关变量
let viewer: any = null
let stationEntities: any[] = []
let warningEntities: any[] = []

// 切换监测站图层
const toggleStationLayer = () => {
  renderStations()
}

// 切换预警图层
const toggleWarningLayer = () => {
  renderWarnings()
}

// 初始化测试数据
const initData = () => {
  // 渲染数据
  renderStations()
  renderWarnings()
}

// 渲染监测站点
const renderStations = async () => {
  if (!viewer) return
  
  const Cesium = await import('cesium')
  
  // 清除现有站点
  stationEntities.forEach(entity => viewer.entities.remove(entity))
  stationEntities = []
  
  if (!layerVisibility.stations) return
  
  // 测试站点数据
  const stations = [
    {
      id: 'station_1',
      name: '香洲区气象站',
      position: { longitude: 113.5767, latitude: 22.2769 },
      type: '自动气象站',
      status: '正常'
    },
    {
      id: 'station_2', 
      name: '金湾区气象站',
      position: { longitude: 113.3820, latitude: 22.1350 },
      type: '自动气象站',
      status: '正常'
    }
  ]
  
  stations.forEach(station => {
    const entity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(
        station.position.longitude,
        station.position.latitude
      ),
      billboard: {
        image: 'data:image/svg+xml;base64,' + btoa(`
          <svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
            <circle cx="16" cy="16" r="12" fill="#00ff00" stroke="#ffffff" stroke-width="2"/>
            <text x="16" y="20" text-anchor="middle" fill="white" font-size="12">站</text>
          </svg>
        `),
        scale: 1.0,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM
      },
      customData: {
        name: station.name,
        type: station.type,
        location: `${station.position.longitude.toFixed(4)}, ${station.position.latitude.toFixed(4)}`,
        description: `状态: ${station.status}`
      }
    })
    
    stationEntities.push(entity)
  })
}

// 渲染预警区域
const renderWarnings = async () => {
  if (!viewer) return
  
  const Cesium = await import('cesium')
  
  // 清除现有预警
  warningEntities.forEach(entity => viewer.entities.remove(entity))
  warningEntities = []
  
  if (!layerVisibility.warnings) return
  
  // 测试预警数据
  const warnings = [
    {
      id: 'warning_1',
      type: '暴雨',
      level: '黄色',
      center: { longitude: 113.5767, latitude: 22.2769 },
      radius: 10000,
      isActive: true,
      description: '预计未来3小时内降雨量将达50毫米以上'
    }
  ]
  
  warnings.forEach(warning => {
    if (!warning.isActive) return
    
    const entity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(
        warning.center.longitude,
        warning.center.latitude
      ),
      ellipse: {
        semiMajorAxis: warning.radius,
        semiMinorAxis: warning.radius,
        material: Cesium.Color.YELLOW.withAlpha(0.4),
        outline: true,
        outlineColor: Cesium.Color.YELLOW.withAlpha(0.8)
      },
      customData: {
        name: `${warning.type}预警`,
        type: `${warning.level}预警`,
        level: warning.level,
        description: warning.description
      }
    })
    
    warningEntities.push(entity)
  })
}

// 定位到珠海市中心
const focusOnZhuhai = async () => {
  if (!viewer) return
  
  try {
    const Cesium = await import('cesium')
    
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(
        ZHUHAI_CENTER.longitude,
        ZHUHAI_CENTER.latitude,
        ZHUHAI_CENTER.height
      ),
      orientation: {
        heading: 0.0,
        pitch: -0.5,
        roll: 0.0
      },
      duration: 3.0
    })
    
    console.log('已定位到珠海市中心')
  } catch (error) {
    console.error('定位失败:', error)
  }
}

// 初始化Cesium
const initCesium = async () => {
  try {
    console.log('开始初始化Cesium...')
    const Cesium = await import('cesium')
    console.log('Cesium模块加载成功')
    
    // 设置Cesium基础URL
    ;(window as any).CESIUM_BASE_URL = '/cesium/'
    
    // 创建最简单稳定的Cesium Viewer
    viewer = new Cesium.Viewer('cesium-container')
    
    console.log('Cesium Viewer创建成功')
    
    // 隐藏不需要的UI元素
    viewer.bottomContainer.style.display = 'none'
    if (viewer.cesiumWidget.creditContainer) {
      viewer.cesiumWidget.creditContainer.style.display = 'none'
    }
    
    // 切换到3D模式
    viewer.scene.mode = Cesium.SceneMode.SCENE3D
    
    // 添加3D Tileset
    try {
      console.log('开始加载3D Tileset...')
      const tileset = viewer.scene.primitives.add(
        await Cesium.Cesium3DTileset.fromIonAssetId(2275207)
      )
      
      console.log('3D Tileset添加成功，等待准备就绪...')
      await tileset.readyPromise
      console.log('3D Tileset准备就绪')
      
    } catch (tilesetError) {
      console.warn('3D Tileset加载失败，使用默认地图:', tilesetError)
    }
    
    // 等待一秒确保所有资源加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 设置初始视角
    await focusOnZhuhai()
    console.log('视角设置完成')
    
    // 添加点击事件处理
    viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(
      (event: any) => {
        const pickedObject = viewer.scene.pick(event.position)
        if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.id)) {
          const entity = pickedObject.id as any
          if (entity.customData) {
            selectedEntity.value = entity.customData
          }
        }
      },
      Cesium.ScreenSpaceEventType.LEFT_CLICK
    )
    
    // 初始化数据
    initData()
    
    // 标记地图加载完成
    isMapLoaded.value = true
    console.log('地图完全加载完成')
    
  } catch (error) {
    console.error('Cesium初始化失败:', error)
    isMapLoaded.value = true
    
    // 显示错误信息
    const container = document.getElementById('cesium-container')
    if (container) {
      container.innerHTML = `
        <div class="flex items-center justify-center h-full bg-gray-900 text-white">
          <div class="text-center">
            <h2 class="text-xl mb-4">地图加载失败</h2>
            <p class="mb-2">请检查网络连接后刷新页面</p>
            <button onclick="location.reload()" class="bg-blue-500 hover:bg-blue-600 px-4 py-2 rounded">
              重新加载
            </button>
          </div>
        </div>
      `
    }
  }
}

// 生命周期
onMounted(() => {
  console.log('珠海市气象预警系统启动')
  initCesium()
})

onUnmounted(() => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})
</script>

<style scoped>
#cesium-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
  overflow: hidden;
}

:deep(.cesium-viewer) {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

:deep(.cesium-viewer-cesiumWidgetContainer) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.cesium-widget) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.cesium-widget canvas) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.cesium-viewer-bottom) {
  display: none !important;
}

:deep(.cesium-viewer-toolbar) {
  display: none !important;
}
</style>
