<template>
  <div class="cesium-test-container">
    <!-- Cesium容器 -->
    <div ref="cesiumContainer" id="cesium-container"></div>
    
    <!-- 状态信息 -->
    <div class="status-panel">
      <h3>Cesium 测试状态</h3>
      <div class="status-item">
        <span class="label">加载状态:</span>
        <span class="value" :class="loadingStatus">{{ getLoadingStatusText() }}</span>
      </div>
      <div class="status-item">
        <span class="label">错误信息:</span>
        <span class="value error">{{ errorMessage || '无' }}</span>
      </div>
      <div class="status-item">
        <span class="label">Viewer状态:</span>
        <span class="value">{{ viewer ? '已创建' : '未创建' }}</span>
      </div>
      
      <div class="test-buttons">
        <button @click="testBasicViewer" :disabled="loading">测试基础Viewer</button>
        <button @click="testWithMarker" :disabled="loading || !viewer">添加标记</button>
        <button @click="clearViewer" :disabled="!viewer">清除Viewer</button>
      </div>
    </div>
    
    <!-- 加载指示器 -->
    <div v-if="loading" class="loading-overlay">
      <div class="spinner"></div>
      <p>正在测试Cesium...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'

// 响应式数据
const cesiumContainer = ref<HTMLDivElement | null>(null)
const loading = ref(false)
const loadingStatus = ref<'idle' | 'loading' | 'success' | 'error'>('idle')
const errorMessage = ref('')
const viewer = ref<any>(null)

// 方法
const getLoadingStatusText = () => {
  const statusTexts = {
    idle: '待测试',
    loading: '加载中',
    success: '成功',
    error: '失败'
  }
  return statusTexts[loadingStatus.value]
}

const testBasicViewer = async () => {
  loading.value = true
  loadingStatus.value = 'loading'
  errorMessage.value = ''
  
  try {
    console.log('开始测试基础Cesium Viewer...')
    
    // 检查容器
    if (!cesiumContainer.value) {
      throw new Error('Cesium容器未找到')
    }
    
    // 清除现有viewer
    if (viewer.value) {
      viewer.value.destroy()
      viewer.value = null
    }
    
    // 动态导入Cesium
    console.log('导入Cesium模块...')
    const Cesium = await import('cesium')
    console.log('Cesium模块导入成功:', Cesium.VERSION)
    
    // 设置Cesium Ion token（可选）
    if (Cesium.Ion) {
      Cesium.Ion.defaultAccessToken = undefined // 不使用token
    }
    
    // 创建最简单的Viewer
    console.log('创建Cesium Viewer...')
    viewer.value = new Cesium.Viewer(cesiumContainer.value, {
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false,
      navigationInstructionsInitiallyVisible: false,
      creditContainer: undefined
    })
    
    console.log('Cesium Viewer创建成功!')
    
    // 设置相机位置到珠海
    viewer.value.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(113.5547, 22.2241, 50000),
      orientation: {
        heading: 0.0,
        pitch: -0.5,
        roll: 0.0
      }
    })
    
    loadingStatus.value = 'success'
    console.log('Cesium测试成功完成!')
    
  } catch (error: any) {
    console.error('Cesium测试失败:', error)
    errorMessage.value = error.message || '未知错误'
    loadingStatus.value = 'error'
  } finally {
    loading.value = false
  }
}

const testWithMarker = async () => {
  if (!viewer.value) return
  
  try {
    const Cesium = await import('cesium')
    
    // 添加珠海市标记
    viewer.value.entities.add({
      position: Cesium.Cartesian3.fromDegrees(113.5547, 22.2241),
      point: {
        pixelSize: 20,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 3
      },
      label: {
        text: '珠海市',
        font: '18pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -60)
      }
    })
    
    console.log('标记添加成功!')
    
  } catch (error: any) {
    console.error('添加标记失败:', error)
    errorMessage.value = error.message
  }
}

const clearViewer = () => {
  if (viewer.value) {
    viewer.value.destroy()
    viewer.value = null
    loadingStatus.value = 'idle'
    errorMessage.value = ''
    console.log('Viewer已清除')
  }
}

// 生命周期
onMounted(() => {
  console.log('Cesium测试组件已挂载')
})

onBeforeUnmount(() => {
  clearViewer()
})

// 暴露方法
defineExpose({
  testBasicViewer,
  clearViewer,
  getViewer: () => viewer.value
})
</script>

<style scoped>
.cesium-test-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #1e3a8a;
  font-family: 'Microsoft YaHei', sans-serif;
}

#cesium-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.status-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 300px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.status-panel h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.label {
  color: #6b7280;
  font-weight: 500;
}

.value {
  font-weight: 600;
}

.value.idle { color: #6b7280; }
.value.loading { color: #3b82f6; }
.value.success { color: #10b981; }
.value.error { color: #ef4444; }

.test-buttons {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: #3b82f6;
  color: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.test-buttons button:hover:not(:disabled) {
  background: #2563eb;
}

.test-buttons button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  color: white;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-overlay p {
  font-size: 16px;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 隐藏Cesium UI */
:deep(.cesium-widget-credits) {
  display: none !important;
}

:deep(.cesium-viewer-bottom) {
  display: none !important;
}

:deep(.cesium-viewer-toolbar) {
  display: none !important;
}
</style>
