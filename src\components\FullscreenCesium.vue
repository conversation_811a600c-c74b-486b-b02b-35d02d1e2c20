<template>
  <div id="cesium-fullscreen">
    <!-- Cesium容器 -->
    <div ref="cesiumContainer" id="cesium-container"></div>
    
    <!-- 加载指示器 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <div class="spinner"></div>
        <p>正在加载珠海市三维地图...</p>
      </div>
    </div>
    
    <!-- 错误信息 -->
    <div v-if="error" class="error-overlay">
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <h3>Cesium加载失败</h3>
        <p>{{ error }}</p>
        <button @click="retryInit" class="retry-btn">重试</button>
      </div>
    </div>

    <!-- 信息面板 -->
    <div v-if="selectedEntity" class="info-panel">
      <h3>{{ selectedEntity.name }}</h3>
      <div class="info-details">
        <p><strong>类型:</strong> {{ selectedEntity.type }}</p>
        <p><strong>位置:</strong> {{ selectedEntity.position }}</p>
      </div>
      <button @click="selectedEntity = null" class="close-btn">关闭</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'

// 响应式数据
const cesiumContainer = ref<HTMLDivElement | null>(null)
const loading = ref(true)
const error = ref('')
const selectedEntity = ref<any>(null)

// Cesium相关变量
let viewer: any = null

// 珠海市中心坐标
const ZHUHAI_CENTER = {
  longitude: 113.5547,
  latitude: 22.2241,
  height: 50000
}

// 初始化Cesium
const initCesium = async () => {
  try {
    loading.value = true
    error.value = ''
    
    console.log('开始初始化全屏Cesium...')
    
    // 动态导入Cesium
    const Cesium = await import('cesium')
    console.log('Cesium模块加载成功')
    
    // 检查容器
    if (!cesiumContainer.value) {
      throw new Error('Cesium容器未找到')
    }
    
    // 创建全屏Cesium Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false,
      navigationInstructionsInitiallyVisible: false,
      creditContainer: undefined,
      requestRenderMode: false,
      maximumRenderTimeChange: Infinity
    })
    
    console.log('Cesium Viewer创建成功')
    
    // 强制设置canvas样式
    const canvas = viewer.cesiumWidget.canvas
    canvas.style.width = '100vw'
    canvas.style.height = '100vh'
    canvas.style.position = 'absolute'
    canvas.style.top = '0'
    canvas.style.left = '0'
    canvas.style.margin = '0'
    canvas.style.padding = '0'
    
    // 设置相机位置到珠海
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        ZHUHAI_CENTER.longitude,
        ZHUHAI_CENTER.latitude,
        ZHUHAI_CENTER.height
      ),
      orientation: {
        heading: 0.0,
        pitch: -0.5,
        roll: 0.0
      }
    })
    
    // 添加珠海市标记
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(113.5547, 22.2241),
      point: {
        pixelSize: 20,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 3
      },
      label: {
        text: '珠海市',
        font: '18pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -60)
      }
    })
    
    // 添加监测站点
    const stations = [
      { name: '香洲监测站', lon: 113.5547, lat: 22.2241 },
      { name: '金湾监测站', lon: 113.3200, lat: 22.1300 },
      { name: '斗门监测站', lon: 113.2100, lat: 22.2100 }
    ]
    
    stations.forEach(station => {
      const entity = viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(station.lon, station.lat),
        point: {
          pixelSize: 15,
          color: Cesium.Color.CYAN,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2
        },
        label: {
          text: station.name,
          font: '14pt sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 1,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -40)
        }
      })
      
      // 添加自定义数据
      ;(entity as any).customData = {
        type: '监测站点',
        name: station.name,
        position: `${station.lon.toFixed(4)}, ${station.lat.toFixed(4)}`
      }
    })
    
    // 添加点击事件
    viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(
      (event: any) => {
        const pickedObject = viewer.scene.pick(event.position)
        if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.id)) {
          const entity = pickedObject.id as any
          if (entity.customData) {
            selectedEntity.value = entity.customData
          }
        }
      },
      Cesium.ScreenSpaceEventType.LEFT_CLICK
    )
    
    // 窗口大小变化监听
    const handleResize = () => {
      if (viewer && viewer.cesiumWidget) {
        viewer.cesiumWidget.resize()
        const canvas = viewer.cesiumWidget.canvas
        canvas.style.width = '100vw'
        canvas.style.height = '100vh'
      }
    }
    
    window.addEventListener('resize', handleResize)
    
    // 强制触发resize
    setTimeout(() => {
      handleResize()
    }, 100)
    
    loading.value = false
    console.log('全屏Cesium初始化完成！')
    
  } catch (err: any) {
    console.error('Cesium初始化失败:', err)
    error.value = err.message || '未知错误'
    loading.value = false
  }
}

// 重试初始化
const retryInit = () => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
  initCesium()
}

// 生命周期
onMounted(() => {
  console.log('全屏Cesium组件挂载')
  setTimeout(initCesium, 100)
})

onBeforeUnmount(() => {
  console.log('清理Cesium资源')
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
  window.removeEventListener('resize', () => {})
})

// 暴露方法
defineExpose({
  getViewer: () => viewer,
  retry: retryInit
})
</script>

<style scoped>
/* 全屏容器 */
#cesium-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  z-index: 0;
}

#cesium-container {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
  position: absolute;
  top: 0;
  left: 0;
}

/* 加载覆盖层 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

/* 错误覆盖层 */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error-content {
  background: white;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.error-icon {
  font-size: 60px;
  margin-bottom: 20px;
}

.error-content h3 {
  color: #e74c3c;
  margin-bottom: 15px;
}

.error-content p {
  color: #666;
  margin-bottom: 20px;
}

.retry-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}

.retry-btn:hover {
  background: #2980b9;
}

/* 信息面板 */
.info-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 300px;
  z-index: 100;
}

.info-panel h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 18px;
}

.info-details p {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.close-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 15px;
  font-size: 14px;
}

.close-btn:hover {
  background: #2980b9;
}

/* 动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 隐藏Cesium UI */
:deep(.cesium-widget-credits) {
  display: none !important;
}

:deep(.cesium-viewer-bottom) {
  display: none !important;
}

:deep(.cesium-viewer-toolbar) {
  display: none !important;
}
</style>
