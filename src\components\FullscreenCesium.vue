<template>
  <div id="cesium-fullscreen" class="w-full h-full">
    <!-- 控制面板 -->
    <div class="absolute top-4 left-4 z-50 bg-black/80 backdrop-blur-sm rounded-lg p-4 text-white min-w-[300px]">
      <h3 class="text-lg font-bold mb-3 text-cyan-400">珠海市气象监测系统</h3>
      
      <!-- 图层控制 -->
      <div class="mb-4">
        <h4 class="text-sm font-semibold mb-2 text-gray-300">图层控制</h4>
        <div class="space-y-2">
          <label class="flex items-center space-x-2 cursor-pointer">
            <input 
              type="checkbox" 
              v-model="layerVisibility.stations"
              @change="toggleStationLayer"
              class="rounded"
            >
            <span class="text-sm">监测站点</span>
          </label>
          <label class="flex items-center space-x-2 cursor-pointer">
            <input 
              type="checkbox" 
              v-model="layerVisibility.warnings"
              @change="toggleWarningLayer"
              class="rounded"
            >
            <span class="text-sm">预警区域</span>
          </label>
        </div>
      </div>

      <!-- 预警类型过滤 -->
      <div class="mb-4">
        <h4 class="text-sm font-semibold mb-2 text-gray-300">预警类型</h4>
        <div class="grid grid-cols-2 gap-2">
          <label v-for="type in Object.values(WeatherWarningType)" :key="type" 
                 class="flex items-center space-x-1 cursor-pointer text-xs">
            <input 
              type="checkbox" 
              :checked="activeWarningTypes.has(type)"
              @change="toggleWarningType(type)"
              class="rounded"
            >
            <span>{{ getWarningTypeName(type) }}</span>
          </label>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex space-x-2">
        <button 
          @click="resetView"
          class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm transition-colors"
        >
          重置视角
        </button>
      </div>
    </div>

    <!-- 信息面板 -->
    <div v-if="selectedEntity" 
         class="absolute top-4 right-4 z-50 bg-black/80 backdrop-blur-sm rounded-lg p-4 text-white min-w-[250px]">
      <h4 class="text-lg font-bold mb-2 text-cyan-400">{{ selectedEntity.name }}</h4>
      <div class="space-y-1 text-sm">
        <p><span class="text-gray-400">类型:</span> {{ selectedEntity.type }}</p>
        <p v-if="selectedEntity.district"><span class="text-gray-400">区域:</span> {{ selectedEntity.district }}</p>
        <p v-if="selectedEntity.level"><span class="text-gray-400">级别:</span> {{ selectedEntity.level }}</p>
        <p v-if="selectedEntity.position"><span class="text-gray-400">位置:</span> {{ selectedEntity.position }}</p>
        <p v-if="selectedEntity.description"><span class="text-gray-400">描述:</span> {{ selectedEntity.description }}</p>
      </div>
      <button 
        @click="selectedEntity = null"
        class="mt-3 px-3 py-1 bg-red-600 hover:bg-red-700 rounded text-sm transition-colors"
      >
        关闭
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive } from 'vue'
import { WeatherWarningType, WarningLevel } from '../types/weather'

// 珠海市中心坐标
const ZHUHAI_CENTER = {
  longitude: 113.5547,
  latitude: 22.2241,
  height: 50000
}

// 响应式数据
const stations = ref<any[]>([])
const warnings = ref<any[]>([])
const selectedEntity = ref<any>(null)

// 图层可见性控制
const layerVisibility = reactive({
  stations: true,
  warnings: true
})

// 活跃的预警类型
const activeWarningTypes = ref(new Set(Object.values(WeatherWarningType)))

// Cesium相关变量
let viewer: any = null
let stationEntities: any[] = []
let warningEntities: any[] = []

// 获取预警类型名称
const getWarningTypeName = (type: WeatherWarningType): string => {
  const names = {
    [WeatherWarningType.TYPHOON]: '台风',
    [WeatherWarningType.HEAVY_RAIN]: '暴雨',
    [WeatherWarningType.THUNDERSTORM]: '雷电',
    [WeatherWarningType.HIGH_WIND]: '大风',
    [WeatherWarningType.FOG]: '大雾',
    [WeatherWarningType.COLD_WAVE]: '寒潮'
  }
  return names[type] || '未知'
}

// 获取预警级别名称
const getWarningLevelName = (level: WarningLevel): string => {
  const names = {
    [WarningLevel.BLUE]: '蓝色',
    [WarningLevel.YELLOW]: '黄色',
    [WarningLevel.ORANGE]: '橙色',
    [WarningLevel.RED]: '红色'
  }
  return names[level] || '未知'
}

// 切换预警类型
const toggleWarningType = (type: WeatherWarningType) => {
  if (activeWarningTypes.value.has(type)) {
    activeWarningTypes.value.delete(type)
  } else {
    activeWarningTypes.value.add(type)
  }
  renderWarnings()
}

// 切换站点图层
const toggleStationLayer = () => {
  renderStations()
}

// 切换预警图层
const toggleWarningLayer = () => {
  renderWarnings()
}

// 重置视角
const resetView = () => {
  focusOnZhuhai()
}

// 定位到珠海市中心
const focusOnZhuhai = async () => {
  if (!viewer) return

  const Cesium = await import('cesium')

  // 根据场景模式选择不同的定位方式
  if (viewer.scene.mode === Cesium.SceneMode.SCENE2D) {
    // 2D模式
    viewer.camera.setView({
      destination: Cesium.Rectangle.fromDegrees(
        ZHUHAI_CENTER.longitude - 0.5,
        ZHUHAI_CENTER.latitude - 0.3,
        ZHUHAI_CENTER.longitude + 0.5,
        ZHUHAI_CENTER.latitude + 0.3
      )
    })
  } else {
    // 3D模式
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(
        ZHUHAI_CENTER.longitude,
        ZHUHAI_CENTER.latitude,
        ZHUHAI_CENTER.height
      ),
      orientation: {
        heading: 0.0,
        pitch: -0.7,
        roll: 0.0
      },
      duration: 2.0
    })
  }
}

// 初始化Cesium
const initCesium = async () => {
  try {
    console.log('开始初始化Cesium...')
    const Cesium = await import('cesium')
    console.log('Cesium模块加载成功')

    // 设置Cesium基础URL
    ;(window as any).CESIUM_BASE_URL = '/cesium/'

    // 创建Cesium Viewer - 使用最简配置
    console.log('创建Cesium Viewer...')

    // 创建最简单的Cesium Viewer - 使用默认配置
    viewer = new Cesium.Viewer('cesium-fullscreen')

    // 隐藏不需要的UI元素
    viewer.cesiumWidget.creditContainer.style.display = 'none'
    viewer.bottomContainer.style.display = 'none'

    // 切换到2D模式
    viewer.scene.mode = Cesium.SceneMode.SCENE2D

    console.log('Cesium Viewer创建成功', viewer)

    // 设置初始视角
    setTimeout(async () => {
      await focusOnZhuhai()
      console.log('视角设置完成')

      // 初始化数据
      initData()
      console.log('数据初始化完成')
    }, 1000)

    // 添加点击事件处理
    viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(
      (event: any) => {
        const pickedObject = viewer.scene.pick(event.position)
        if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.id)) {
          const entity = pickedObject.id as any
          if (entity.customData) {
            selectedEntity.value = entity.customData
          }
        }
      },
      Cesium.ScreenSpaceEventType.LEFT_CLICK
    )

  } catch (error) {
    console.error('Cesium初始化失败:', error)
  }
}

// 初始化数据
const initData = () => {
  // 创建静态测试数据
  stations.value = [
    {
      id: 'station-001',
      name: '香洲监测站',
      position: { longitude: 113.5547, latitude: 22.2241, height: 10 },
      district: '香洲区',
      temperature: 28.5,
      humidity: 75
    },
    {
      id: 'station-002',
      name: '金湾监测站',
      position: { longitude: 113.3200, latitude: 22.1300, height: 25 },
      district: '金湾区',
      temperature: 29.2,
      humidity: 72
    }
  ]
  
  warnings.value = [
    {
      id: 'warning-001',
      type: WeatherWarningType.HEAVY_RAIN,
      level: WarningLevel.YELLOW,
      center: { longitude: 113.4500, latitude: 22.2000, height: 0 },
      radius: 8000,
      description: '预计未来6小时内，金湾区将出现50-80mm降雨',
      isActive: true
    }
  ]
  
  // 渲染数据
  renderStations()
  renderWarnings()
}

// 渲染监测站点
const renderStations = async () => {
  if (!viewer) return
  
  const Cesium = await import('cesium')
  
  // 清除现有站点
  stationEntities.forEach(entity => viewer.entities.remove(entity))
  stationEntities = []
  
  if (!layerVisibility.stations) return
  
  stations.value.forEach(station => {
    const entity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(
        station.position.longitude,
        station.position.latitude
      ),
      point: {
        pixelSize: 12,
        color: Cesium.Color.LIME,
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2
      },
      label: {
        text: station.name,
        font: 'bold 12pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        pixelOffset: new Cesium.Cartesian2(0, -35)
      }
    })
    
    ;(entity as any).customData = {
      type: '监测站点',
      name: station.name,
      district: station.district,
      position: `${station.position.longitude.toFixed(4)}, ${station.position.latitude.toFixed(4)}`
    }
    
    stationEntities.push(entity)
  })
}

// 渲染预警区域
const renderWarnings = async () => {
  if (!viewer) return
  
  const Cesium = await import('cesium')
  
  // 清除现有预警
  warningEntities.forEach(entity => viewer.entities.remove(entity))
  warningEntities = []
  
  if (!layerVisibility.warnings) return
  
  warnings.value
    .filter(w => w.isActive && activeWarningTypes.value.has(w.type))
    .forEach(warning => {
      const entity = viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(
          warning.center.longitude,
          warning.center.latitude
        ),
        ellipse: {
          semiMajorAxis: warning.radius,
          semiMinorAxis: warning.radius,
          material: Cesium.Color.YELLOW.withAlpha(0.4),
          outline: true,
          outlineColor: Cesium.Color.YELLOW.withAlpha(0.8)
        },
        label: {
          text: getWarningTypeName(warning.type),
          font: 'bold 14pt Microsoft YaHei',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          pixelOffset: new Cesium.Cartesian2(0, -20)
        }
      })
      
      ;(entity as any).customData = {
        type: '预警区域',
        name: getWarningTypeName(warning.type),
        level: getWarningLevelName(warning.level),
        description: warning.description
      }
      
      warningEntities.push(entity)
    })
}

onMounted(() => {
  initCesium()
})

onUnmounted(() => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})
</script>

<style scoped>
#cesium-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
  overflow: hidden;
}

/* 确保Cesium容器填满整个屏幕 */
:deep(.cesium-viewer) {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

:deep(.cesium-viewer-cesiumWidgetContainer) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.cesium-widget) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.cesium-widget canvas) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.cesium-viewer-bottom) {
  display: none !important;
}

:deep(.cesium-viewer-toolbar) {
  display: none !important;
}
</style>
