<template>
  <div id="cesium-fullscreen">
    <!-- Cesium容器 -->
    <div ref="cesiumContainer" id="cesium-container"></div>
    
    <!-- 加载指示器 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <div class="spinner"></div>
        <p>正在加载珠海市三维地图...</p>
      </div>
    </div>
    
    <!-- 错误信息 -->
    <div v-if="error" class="error-overlay">
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <h3>Cesium加载失败</h3>
        <p>{{ error }}</p>
        <button @click="retryInit" class="retry-btn">重试</button>
      </div>
    </div>

    <!-- 快速控制面板 -->
    <div class="control-panel">
      <button @click="focusOnZhuhai" class="control-btn" title="回到珠海市中心">
        🏠 珠海市
      </button>
      <button @click="zoomIn" class="control-btn" title="放大">
        🔍 放大
      </button>
      <button @click="zoomOut" class="control-btn" title="缩小">
        🔍 缩小
      </button>
      <button @click="resetView" class="control-btn" title="重置视角">
        🔄 重置
      </button>
    </div>

    <!-- 信息面板 -->
    <div v-if="selectedEntity" class="info-panel">
      <h3>{{ selectedEntity.name }}</h3>
      <div class="info-details">
        <p><strong>类型:</strong> {{ selectedEntity.type }}</p>
        <p><strong>区域:</strong> {{ selectedEntity.district || '未知' }}</p>
        <p><strong>位置:</strong> {{ selectedEntity.position }}</p>
      </div>
      <button @click="selectedEntity = null" class="close-btn">关闭</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { weatherWarningService } from '@/services/weatherWarningService'
import { WeatherWarningType, WarningLevel } from '@/types/weather'
import type { WeatherWarningArea, MonitoringStation } from '@/types/weather'

// 定义事件
const emit = defineEmits<{
  'entity-select': [entity: any]
  'cesium-error': [error: any]
}>()

// 响应式数据
const cesiumContainer = ref<HTMLDivElement | null>(null)
const loading = ref(true)
const error = ref('')
const selectedEntity = ref<any>(null)

// 气象数据
const warnings = ref<WeatherWarningArea[]>([])
const stations = ref<MonitoringStation[]>([])
const activeWarningTypes = ref<Set<WeatherWarningType>>(new Set([
  WeatherWarningType.HEAVY_RAIN,
  WeatherWarningType.STRONG_WIND
]))

// 图层显示控制
const layerVisibility = ref({
  stations: true,
  warnings: true,
  boundaries: true
})

// Cesium相关变量
let viewer: any = null
let warningEntities: any[] = []
let stationEntities: any[] = []

// 珠海市中心坐标 - 优化视角
const ZHUHAI_CENTER = {
  longitude: 113.5547,
  latitude: 22.2241,
  height: 25000  // 降低高度，更近距离观察珠海
}

// 初始化Cesium
const initCesium = async () => {
  try {
    loading.value = true
    error.value = ''
    
    console.log('开始初始化全屏Cesium...')
    
    // 动态导入Cesium
    const Cesium = await import('cesium')
    console.log('Cesium模块加载成功')
    
    // 检查容器
    if (!cesiumContainer.value) {
      throw new Error('Cesium容器未找到')
    }
    
    // 创建全屏Cesium Viewer - 优化性能配置
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false,
      navigationInstructionsInitiallyVisible: false,
      creditContainer: undefined,
      requestRenderMode: false,
      maximumRenderTimeChange: Infinity,
      // 优化性能设置
      targetFrameRate: 60,
      resolutionScale: 1.0
    })

    // 优化相机控制器设置，提升缩放响应速度
    const cameraController = viewer.scene.screenSpaceCameraController
    cameraController.zoomEventTypes = [
      Cesium.CameraEventType.WHEEL,
      Cesium.CameraEventType.PINCH
    ]
    cameraController.tiltEventTypes = [
      Cesium.CameraEventType.PINCH,
      Cesium.CameraEventType.RIGHT_DRAG
    ]

    // 设置更快的缩放速度
    cameraController.minimumZoomDistance = 100      // 最小缩放距离100米
    cameraController.maximumZoomDistance = 500000   // 最大缩放距离500公里
    cameraController.wheelZoomFactor = 10           // 滚轮缩放倍数（默认为10）
    cameraController.pinchZoomFactor = 2            // 触摸缩放倍数

    // 设置更灵敏的旋转和平移
    cameraController.rotateEventTypes = [Cesium.CameraEventType.LEFT_DRAG]
    cameraController.translateEventTypes = [Cesium.CameraEventType.RIGHT_DRAG]
    cameraController.lookEventTypes = [Cesium.CameraEventType.LEFT_DRAG]

    // 启用惯性效果，让操作更流畅
    cameraController.enableRotate = true
    cameraController.enableTranslate = true
    cameraController.enableZoom = true
    cameraController.enableTilt = true
    cameraController.enableLook = true

    // 设置惯性参数
    cameraController.inertiaSpin = 0.9     // 旋转惯性
    cameraController.inertiaTranslate = 0.9 // 平移惯性
    cameraController.inertiaZoom = 0.8     // 缩放惯性
    
    console.log('Cesium Viewer创建成功')
    
    // 强制设置canvas样式
    const canvas = viewer.cesiumWidget.canvas
    canvas.style.width = '100vw'
    canvas.style.height = '100vh'
    canvas.style.position = 'absolute'
    canvas.style.top = '0'
    canvas.style.left = '0'
    canvas.style.margin = '0'
    canvas.style.padding = '0'
    
    // 设置相机位置到珠海市中心 - 优化视角
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        ZHUHAI_CENTER.longitude,
        ZHUHAI_CENTER.latitude,
        ZHUHAI_CENTER.height
      ),
      orientation: {
        heading: 0.0,           // 正北方向
        pitch: -0.7,            // 更陡的俯视角度，更好地看到珠海全貌
        roll: 0.0
      }
    })

    // 设置相机的飞行动画参数，让缩放更流畅
    viewer.camera.defaultMoveAmount = 100.0
    viewer.camera.defaultLookAmount = Math.PI / 60.0
    viewer.camera.defaultRotateAmount = Math.PI / 3600.0
    viewer.camera.defaultZoomAmount = 100.0

    // 设置场景的渲染优化
    viewer.scene.globe.maximumScreenSpaceError = 1.5  // 降低地形细节以提升性能
    viewer.scene.globe.tileCacheSize = 1000           // 增加瓦片缓存

    // 启用大气效果但优化性能
    viewer.scene.skyAtmosphere.show = true
    viewer.scene.globe.showGroundAtmosphere = true
    viewer.scene.globe.enableLighting = false  // 关闭光照以提升性能
    
    // 添加珠海市中心标记 - 更突出的显示
    const zhuhaiMainEntity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(113.5547, 22.2241),
      point: {
        pixelSize: 25,
        color: Cesium.Color.GOLD,
        outlineColor: Cesium.Color.DARKRED,
        outlineWidth: 4,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        scaleByDistance: new Cesium.NearFarScalar(1000, 2.0, 100000, 0.5)  // 距离缩放
      },
      label: {
        text: '珠海市中心',
        font: 'bold 20pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 3,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -70),
        scaleByDistance: new Cesium.NearFarScalar(1000, 1.5, 100000, 0.3),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    })

    // 添加珠海市边界圆圈，帮助定位
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(113.5547, 22.2241),
      ellipse: {
        semiMajorAxis: 15000,  // 15公里半径
        semiMinorAxis: 15000,
        material: Cesium.Color.GOLD.withAlpha(0.2),
        outline: true,
        outlineColor: Cesium.Color.GOLD,
        height: 0,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    })
    
    // 使用简化的静态数据，避免服务依赖问题
    try {
      // 创建简化的静态数据
      stations.value = [
        {
          id: 'station-001',
          name: '香洲监测站',
          position: { longitude: 113.5547, latitude: 22.2241, height: 10 },
          altitude: 10,
          temperature: 28.5,
          humidity: 75,
          windSpeed: 12.3,
          windDirection: 135,
          pressure: 1013.2,
          visibility: 15.8,
          lastUpdate: new Date(),
          district: '香洲区',
          stationType: 'automatic'
        },
        {
          id: 'station-002',
          name: '金湾监测站',
          position: { longitude: 113.3200, latitude: 22.1300, height: 25 },
          altitude: 25,
          temperature: 29.2,
          humidity: 72,
          windSpeed: 18.9,
          windDirection: 150,
          pressure: 1011.5,
          visibility: 18.2,
          lastUpdate: new Date(),
          district: '金湾区',
          stationType: 'automatic'
        }
      ]

      warnings.value = [
        {
          id: 'warning-001',
          type: WeatherWarningType.HEAVY_RAIN,
          level: WarningLevel.YELLOW,
          center: { longitude: 113.4500, latitude: 22.2000, height: 0 },
          radius: 8000,
          intensity: 0.7,
          startTime: new Date(),
          endTime: new Date(Date.now() + 6 * 60 * 60 * 1000),
          description: '预计未来6小时内，金湾区将出现50-80mm降雨',
          affectedStations: ['station-002'],
          isActive: true
        }
      ]

      console.log('使用静态数据:', { stations: stations.value.length, warnings: warnings.value.length })

      // 渲染监测站点
      renderStations()

      // 渲染预警区域
      renderWarnings()

    } catch (error) {
      console.error('数据初始化失败:', error)
      // 即使失败也要确保数组存在
      stations.value = []
      warnings.value = []
    }
    
    stations.forEach((station, index) => {
      // 根据区域设置不同颜色
      let stationColor = Cesium.Color.CYAN
      if (station.district === '香洲区') {
        stationColor = Cesium.Color.LIME
      } else if (station.district === '金湾区') {
        stationColor = Cesium.Color.ORANGE
      } else if (station.district === '斗门区') {
        stationColor = Cesium.Color.VIOLET
      }

      const entity = viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(station.lon, station.lat),
        point: {
          pixelSize: 12,
          color: stationColor,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          scaleByDistance: new Cesium.NearFarScalar(1000, 1.5, 50000, 0.3)
        },
        label: {
          text: station.name,
          font: 'bold 12pt Microsoft YaHei',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -35),
          scaleByDistance: new Cesium.NearFarScalar(1000, 1.0, 50000, 0.2),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      })

      // 添加自定义数据
      ;(entity as any).customData = {
        type: '监测站点',
        name: station.name,
        district: station.district,
        position: `${station.lon.toFixed(4)}, ${station.lat.toFixed(4)}`
      }
    })
    
    // 添加点击事件
    viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(
      (event: any) => {
        const pickedObject = viewer.scene.pick(event.position)
        if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.id)) {
          const entity = pickedObject.id as any
          if (entity.customData) {
            selectedEntity.value = entity.customData
          }
        }
      },
      Cesium.ScreenSpaceEventType.LEFT_CLICK
    )
    
    // 窗口大小变化监听 - 优化响应速度
    const handleResize = () => {
      if (viewer && viewer.cesiumWidget) {
        viewer.cesiumWidget.resize()
        const canvas = viewer.cesiumWidget.canvas
        canvas.style.width = '100vw'
        canvas.style.height = '100vh'

        // 重新定位到珠海市中心
        setTimeout(() => {
          focusOnZhuhai()
        }, 100)
      }
    }

    // 定位到珠海市中心的函数
    const focusOnZhuhai = () => {
      if (viewer) {
        viewer.camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(
            ZHUHAI_CENTER.longitude,
            ZHUHAI_CENTER.latitude,
            ZHUHAI_CENTER.height
          ),
          orientation: {
            heading: 0.0,
            pitch: -0.7,
            roll: 0.0
          },
          duration: 1.5,  // 1.5秒飞行时间
          easingFunction: Cesium.EasingFunction.CUBIC_IN_OUT
        })
      }
    }

    window.addEventListener('resize', handleResize)

    // 强制触发resize和定位
    setTimeout(() => {
      handleResize()
      focusOnZhuhai()
    }, 200)
    
    loading.value = false
    console.log('全屏Cesium初始化完成！')
    
  } catch (err: any) {
    console.error('Cesium初始化失败:', err)
    error.value = err.message || '未知错误'
    loading.value = false

    // 发射错误事件给父组件
    emit('cesium-error', err)
  }
}

// 重试初始化
const retryInit = () => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
  initCesium()
}

// 控制面板方法
const focusOnZhuhai = async () => {
  if (viewer) {
    const Cesium = await import('cesium')
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(
        ZHUHAI_CENTER.longitude,
        ZHUHAI_CENTER.latitude,
        ZHUHAI_CENTER.height
      ),
      orientation: {
        heading: 0.0,
        pitch: -0.7,
        roll: 0.0
      },
      duration: 1.5,
      easingFunction: Cesium.EasingFunction.CUBIC_IN_OUT
    })
  }
}

const zoomIn = async () => {
  if (viewer) {
    const Cesium = await import('cesium')
    const currentHeight = viewer.camera.positionCartographic.height
    const newHeight = Math.max(currentHeight * 0.5, 100)  // 最小100米

    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(
        ZHUHAI_CENTER.longitude,
        ZHUHAI_CENTER.latitude,
        newHeight
      ),
      duration: 1.0
    })
  }
}

const zoomOut = async () => {
  if (viewer) {
    const Cesium = await import('cesium')
    const currentHeight = viewer.camera.positionCartographic.height
    const newHeight = Math.min(currentHeight * 2.0, 500000)  // 最大500公里

    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(
        ZHUHAI_CENTER.longitude,
        ZHUHAI_CENTER.latitude,
        newHeight
      ),
      duration: 1.0
    })
  }
}

const resetView = () => {
  focusOnZhuhai()
}

// 渲染监测站点
const renderStations = async () => {
  try {
    if (!viewer) {
      console.log('Viewer未创建，跳过站点渲染')
      return
    }

    const Cesium = await import('cesium')

    // 清除现有站点
    if (Array.isArray(stationEntities)) {
      stationEntities.forEach(entity => {
        try {
          viewer.entities.remove(entity)
        } catch (e) {
          console.warn('移除站点实体失败:', e)
        }
      })
    }
    stationEntities = []

    if (!layerVisibility.value.stations) {
      console.log('站点图层已隐藏')
      return
    }

    // 确保stations是数组
    if (!Array.isArray(stations.value)) {
      console.warn('stations.value 不是数组:', stations.value)
      return
    }

    console.log('开始渲染', stations.value.length, '个监测站点')

    stations.value.forEach((station, index) => {
      try {
        // 根据区域设置不同颜色
        let stationColor = Cesium.Color.CYAN
        if (station.district === '香洲区') {
          stationColor = Cesium.Color.LIME
        } else if (station.district === '金湾区') {
          stationColor = Cesium.Color.ORANGE
        } else if (station.district === '斗门区') {
          stationColor = Cesium.Color.VIOLET
        }

        const entity = viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(
            station.position.longitude,
            station.position.latitude
          ),
          point: {
            pixelSize: 12,
            color: stationColor,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            scaleByDistance: new Cesium.NearFarScalar(1000, 1.5, 50000, 0.3)
          },
          label: {
            text: station.name,
            font: 'bold 12pt Microsoft YaHei',
            fillColor: Cesium.Color.WHITE,
            outlineColor: Cesium.Color.BLACK,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -35),
            scaleByDistance: new Cesium.NearFarScalar(1000, 1.0, 50000, 0.2),
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          }
        })

        // 添加自定义数据
        ;(entity as any).customData = {
          type: '监测站点',
          name: station.name,
          district: station.district,
          position: `${station.position.longitude.toFixed(4)}, ${station.position.latitude.toFixed(4)}`,
          data: station
        }

        stationEntities.push(entity)
        console.log(`站点 ${station.name} 渲染成功`)

      } catch (stationError) {
        console.error(`渲染站点 ${station.name} 失败:`, stationError)
      }
    })

    console.log(`成功渲染 ${stationEntities.length} 个监测站点`)

  } catch (error) {
    console.error('渲染监测站点失败:', error)
  }
}
}

// 渲染预警区域
const renderWarnings = async () => {
  try {
    if (!viewer) {
      console.log('Viewer未创建，跳过预警渲染')
      return
    }

    const Cesium = await import('cesium')

    // 清除现有预警
    if (Array.isArray(warningEntities)) {
      warningEntities.forEach(entity => {
        try {
          viewer.entities.remove(entity)
        } catch (e) {
          console.warn('移除预警实体失败:', e)
        }
      })
    }
    warningEntities = []

    if (!layerVisibility.value.warnings) {
      console.log('预警图层已隐藏')
      return
    }

    // 确保warnings是数组
    if (!Array.isArray(warnings.value)) {
      console.warn('warnings.value 不是数组:', warnings.value)
      return
    }

    const activeWarnings = warnings.value.filter(w => w.isActive && activeWarningTypes.value.has(w.type))
    console.log('开始渲染', activeWarnings.length, '个预警区域')

    activeWarnings.forEach((warning, index) => {
      try {
        // 根据预警级别设置颜色
        let warningColor = Cesium.Color.YELLOW
        switch (warning.level) {
          case WarningLevel.BLUE:
            warningColor = Cesium.Color.BLUE.withAlpha(0.3)
            break
          case WarningLevel.YELLOW:
            warningColor = Cesium.Color.YELLOW.withAlpha(0.4)
            break
          case WarningLevel.ORANGE:
            warningColor = Cesium.Color.ORANGE.withAlpha(0.5)
            break
          case WarningLevel.RED:
            warningColor = Cesium.Color.RED.withAlpha(0.6)
            break
        }

        // 创建预警区域圆圈
        const entity = viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(
            warning.center.longitude,
            warning.center.latitude
          ),
          ellipse: {
            semiMajorAxis: warning.radius,
            semiMinorAxis: warning.radius,
            material: warningColor,
            outline: true,
            outlineColor: warningColor.withAlpha(0.8),
            height: 0,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          },
          label: {
            text: getWarningTypeName(warning.type),
            font: 'bold 14pt Microsoft YaHei',
            fillColor: Cesium.Color.WHITE,
            outlineColor: Cesium.Color.BLACK,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -20),
            scaleByDistance: new Cesium.NearFarScalar(1000, 1.0, 100000, 0.3)
          }
        })

        // 添加自定义数据
        ;(entity as any).customData = {
          type: '预警区域',
          name: getWarningTypeName(warning.type),
          level: getWarningLevelName(warning.level),
          description: warning.description,
          data: warning
        }

        warningEntities.push(entity)
        console.log(`预警 ${getWarningTypeName(warning.type)} 渲染成功`)

      } catch (warningError) {
        console.error(`渲染预警失败:`, warningError)
      }
    })

    console.log(`成功渲染 ${warningEntities.length} 个预警区域`)

  } catch (error) {
    console.error('渲染预警区域失败:', error)
  }
}
}

// 更新Cesium实体
const updateCesiumEntities = () => {
  renderStations()
  renderWarnings()
}

// 获取预警类型名称
const getWarningTypeName = (type: WeatherWarningType) => {
  const names = {
    [WeatherWarningType.HEAVY_RAIN]: '暴雨预警',
    [WeatherWarningType.STRONG_WIND]: '大风预警',
    [WeatherWarningType.GALE]: '强风预警',
    [WeatherWarningType.TORRENTIAL_RAIN]: '强降雨预警'
  }
  return names[type] || '未知预警'
}

// 获取预警级别名称
const getWarningLevelName = (level: WarningLevel) => {
  const names = {
    [WarningLevel.BLUE]: '蓝色预警',
    [WarningLevel.YELLOW]: '黄色预警',
    [WarningLevel.ORANGE]: '橙色预警',
    [WarningLevel.RED]: '红色预警'
  }
  return names[level] || '未知级别'
}

// 生命周期
onMounted(() => {
  console.log('全屏Cesium组件挂载')
  setTimeout(initCesium, 100)
})

onBeforeUnmount(() => {
  console.log('清理Cesium资源')
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
  window.removeEventListener('resize', () => {})
})

// 暴露方法
defineExpose({
  getViewer: () => viewer,
  retry: retryInit,
  focusOnZhuhai: () => {
    if (viewer) {
      viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
          ZHUHAI_CENTER.longitude,
          ZHUHAI_CENTER.latitude,
          ZHUHAI_CENTER.height
        ),
        orientation: {
          heading: 0.0,
          pitch: -0.7,
          roll: 0.0
        },
        duration: 1.5,
        easingFunction: (window as any).Cesium?.EasingFunction?.CUBIC_IN_OUT
      })
    }
  },
  zoomToStation: (stationName: string) => {
    if (viewer) {
      const station = stations.find(s => s.name === stationName)
      if (station) {
        viewer.camera.flyTo({
          destination: (window as any).Cesium.Cartesian3.fromDegrees(
            station.lon,
            station.lat,
            5000  // 5公里高度查看站点
          ),
          duration: 2.0
        })
      }
    }
  }
})
</script>

<style scoped>
/* 全屏容器 */
#cesium-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  z-index: 0;
}

#cesium-container {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
  position: absolute;
  top: 0;
  left: 0;
}

/* 加载覆盖层 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

/* 错误覆盖层 */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error-content {
  background: white;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.error-icon {
  font-size: 60px;
  margin-bottom: 20px;
}

.error-content h3 {
  color: #e74c3c;
  margin-bottom: 15px;
}

.error-content p {
  color: #666;
  margin-bottom: 20px;
}

.retry-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}

.retry-btn:hover {
  background: #2980b9;
}

/* 快速控制面板 */
.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 100;
}

.control-btn {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  min-width: 100px;
  text-align: left;
}

.control-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.control-btn:active {
  transform: translateY(0);
}

/* 信息面板 */
.info-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 300px;
  z-index: 100;
}

.info-panel h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 18px;
}

.info-details p {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.close-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 15px;
  font-size: 14px;
}

.close-btn:hover {
  background: #2980b9;
}

/* 动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 隐藏Cesium UI */
:deep(.cesium-widget-credits) {
  display: none !important;
}

:deep(.cesium-viewer-bottom) {
  display: none !important;
}

:deep(.cesium-viewer-toolbar) {
  display: none !important;
}
</style>
