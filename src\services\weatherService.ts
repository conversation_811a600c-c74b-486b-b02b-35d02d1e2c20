import { WeatherWarningType, WarningLevel } from '@/types/weather'
import type {
  WeatherWarningArea,
  MonitoringStation,
  GeographicPosition
} from '@/types/weather'
import { zhuhaiStationPositions, zhuhaiConfig } from '@/config/zhuhaiConfig'

// 模拟珠海市监测站点数据
export const generateMockStations = (): MonitoringStation[] => {
  const stations: MonitoringStation[] = []

  const stationNames = [
    '拱北气象站', '吉大气象站', '前山气象站', '湾仔气象站',
    '三灶气象站', '红旗气象站', '平沙气象站',
    '井岸气象站', '白蕉气象站', '斗门气象站'
  ]

  const districts = ['香洲区', '香洲区', '香洲区', '香洲区', '金湾区', '金湾区', '金湾区', '斗门区', '斗门区', '斗门区']
  const stationTypes: ('automatic' | 'manual' | 'radar')[] = ['automatic', 'automatic', 'manual', 'automatic', 'radar', 'automatic', 'manual', 'automatic', 'automatic', 'radar']

  for (let i = 0; i < Math.min(stationNames.length, zhuhaiStationPositions.length); i++) {
    const position = zhuhaiStationPositions[i]

    stations.push({
      id: `zhuhai-station-${i + 1}`,
      name: stationNames[i],
      position: {
        longitude: position.longitude,
        latitude: position.latitude,
        height: position.height
      },
      altitude: position.height || Math.random() * 50 + 5,
      temperature: Math.random() * 15 + 20, // 珠海气温范围 20-35°C
      humidity: Math.random() * 30 + 60,     // 湿度 60-90%
      windSpeed: Math.random() * 15 + 2,     // 风速 2-17 m/s
      windDirection: Math.random() * 360,
      pressure: Math.random() * 20 + 1005,   // 气压 1005-1025 hPa
      visibility: Math.random() * 10 + 10,   // 能见度 10-20 km
      lastUpdate: new Date(),
      district: districts[i],
      stationType: stationTypes[i]
    })
  }

  return stations
}

// 模拟珠海市预警区域数据
export const generateMockWarnings = (): WeatherWarningArea[] => {
  const warnings: WeatherWarningArea[] = []

  const warningTypes = [
    WeatherWarningType.HEAVY_RAIN,
    WeatherWarningType.STRONG_WIND,
    WeatherWarningType.GALE,
    WeatherWarningType.TORRENTIAL_RAIN
  ]

  const warningLevels = [WarningLevel.BLUE, WarningLevel.YELLOW, WarningLevel.ORANGE, WarningLevel.RED]

  // 在珠海市范围内生成预警区域
  for (let i = 0; i < 4; i++) {
    const type = warningTypes[i % warningTypes.length]
    const level = warningLevels[Math.floor(Math.random() * warningLevels.length)]

    // 在珠海市边界内随机生成位置
    const longitude = Math.random() * (zhuhaiConfig.bounds.east - zhuhaiConfig.bounds.west) + zhuhaiConfig.bounds.west
    const latitude = Math.random() * (zhuhaiConfig.bounds.north - zhuhaiConfig.bounds.south) + zhuhaiConfig.bounds.south

    // 选择一个区域作为影响区域
    const district = zhuhaiConfig.districts[Math.floor(Math.random() * zhuhaiConfig.districts.length)]

    warnings.push({
      id: `zhuhai-warning-${i + 1}`,
      type,
      level,
      center: {
        longitude,
        latitude,
        height: 100
      },
      radius: Math.random() * 5000 + 2000, // 2-7公里半径
      intensity: Math.random() * 0.5 + 0.5,
      startTime: new Date(Date.now() - Math.random() * 3600000),
      endTime: new Date(Date.now() + Math.random() * 7200000),
      description: `${getWarningTypeName(type)}${getWarningLevelName(level)}`,
      affectedStations: [],
      district: district.name
    })
  }

  return warnings
}

// 获取预警类型名称
export const getWarningTypeName = (type: WeatherWarningType): string => {
  switch (type) {
    case WeatherWarningType.HEAVY_RAIN:
      return '暴雨'
    case WeatherWarningType.STRONG_WIND:
      return '狂风'
    case WeatherWarningType.GALE:
      return '强风'
    case WeatherWarningType.TORRENTIAL_RAIN:
      return '强降雨'
    default:
      return '未知'
  }
}

// 获取预警级别名称
export const getWarningLevelName = (level: WarningLevel): string => {
  switch (level) {
    case WarningLevel.BLUE:
      return '蓝色预警'
    case WarningLevel.YELLOW:
      return '黄色预警'
    case WarningLevel.ORANGE:
      return '橙色预警'
    case WarningLevel.RED:
      return '红色预警'
    default:
      return '未知级别'
  }
}

// 实时更新监测站点数据
export const updateStationData = (stations: MonitoringStation[]): MonitoringStation[] => {
  return stations.map(station => ({
    ...station,
    temperature: station.temperature + (Math.random() - 0.5) * 2,
    humidity: Math.max(0, Math.min(100, station.humidity + (Math.random() - 0.5) * 5)),
    windSpeed: Math.max(0, station.windSpeed + (Math.random() - 0.5) * 3),
    windDirection: (station.windDirection + (Math.random() - 0.5) * 10) % 360,
    pressure: station.pressure + (Math.random() - 0.5) * 5,
    visibility: Math.max(0, station.visibility + (Math.random() - 0.5) * 2),
    lastUpdate: new Date()
  }))
}

// 动态生成新的预警
export const generateRandomWarning = (): WeatherWarningArea => {
  const warningTypes = [
    WeatherWarningType.HEAVY_RAIN,
    WeatherWarningType.STRONG_WIND,
    WeatherWarningType.GALE,
    WeatherWarningType.TORRENTIAL_RAIN
  ]

  const warningLevels = [WarningLevel.BLUE, WarningLevel.YELLOW, WarningLevel.ORANGE, WarningLevel.RED]

  const type = warningTypes[Math.floor(Math.random() * warningTypes.length)]
  const level = warningLevels[Math.floor(Math.random() * warningLevels.length)]

  // 在珠海市边界内随机生成位置
  const longitude = Math.random() * (zhuhaiConfig.bounds.east - zhuhaiConfig.bounds.west) + zhuhaiConfig.bounds.west
  const latitude = Math.random() * (zhuhaiConfig.bounds.north - zhuhaiConfig.bounds.south) + zhuhaiConfig.bounds.south

  // 选择一个区域作为影响区域
  const district = zhuhaiConfig.districts[Math.floor(Math.random() * zhuhaiConfig.districts.length)]

  return {
    id: `warning-${Date.now()}`,
    type,
    level,
    center: {
      longitude,
      latitude,
      height: 100
    },
    radius: Math.random() * 5000 + 2000, // 2-7公里半径
    intensity: Math.random() * 0.5 + 0.5,
    startTime: new Date(),
    endTime: new Date(Date.now() + Math.random() * 7200000),
    description: `${getWarningTypeName(type)}${getWarningLevelName(level)}`,
    affectedStations: [],
    district: district.name
  }
}

// 检查站点是否在预警区域内（使用地理坐标计算）
export const isStationInWarningArea = (station: MonitoringStation, warning: WeatherWarningArea): boolean => {
  // 使用简化的距离计算（适用于小范围地理区域）
  const earthRadius = 6371000 // 地球半径（米）
  const lat1 = station.position.latitude * Math.PI / 180
  const lat2 = warning.center.latitude * Math.PI / 180
  const deltaLat = (warning.center.latitude - station.position.latitude) * Math.PI / 180
  const deltaLon = (warning.center.longitude - station.position.longitude) * Math.PI / 180

  const a = Math.sin(deltaLat/2) * Math.sin(deltaLat/2) +
            Math.cos(lat1) * Math.cos(lat2) *
            Math.sin(deltaLon/2) * Math.sin(deltaLon/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  const distance = earthRadius * c

  return distance <= warning.radius
}

// 更新预警区域影响的站点
export const updateAffectedStations = (
  warnings: WeatherWarningArea[], 
  stations: MonitoringStation[]
): WeatherWarningArea[] => {
  return warnings.map(warning => ({
    ...warning,
    affectedStations: stations
      .filter(station => isStationInWarningArea(station, warning))
      .map(station => station.id)
  }))
}

// 气象数据服务类
export class WeatherDataService {
  private stations: MonitoringStation[] = []
  private warnings: WeatherWarningArea[] = []
  private updateInterval: number | null = null
  
  constructor() {
    this.stations = generateMockStations()
    this.warnings = generateMockWarnings()
    this.updateAffectedStations()
  }
  
  getStations(): MonitoringStation[] {
    return this.stations
  }
  
  getWarnings(): WeatherWarningArea[] {
    return this.warnings
  }
  
  startRealTimeUpdates(callback: (stations: MonitoringStation[], warnings: WeatherWarningArea[]) => void) {
    this.updateInterval = window.setInterval(() => {
      this.stations = updateStationData(this.stations)
      
      // 随机添加新预警
      if (Math.random() < 0.1 && this.warnings.length < 8) {
        this.warnings.push(generateRandomWarning())
      }
      
      // 移除过期预警
      this.warnings = this.warnings.filter(warning => warning.endTime > new Date())
      
      this.updateAffectedStations()
      callback(this.stations, this.warnings)
    }, 5000) // 每5秒更新一次
  }
  
  stopRealTimeUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
    }
  }
  
  private updateAffectedStations() {
    this.warnings = updateAffectedStations(this.warnings, this.stations)
  }
}
