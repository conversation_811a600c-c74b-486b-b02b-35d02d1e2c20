<template>
  <div class="w-full h-full bg-gray-900 text-white">
    <div class="p-4">
      <h1 class="text-2xl font-bold mb-4">Cesium测试页面</h1>
      
      <div class="mb-4">
        <button 
          @click="testCesiumImport"
          class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded mr-2"
        >
          测试Cesium导入
        </button>
        
        <button 
          @click="testBasicViewer"
          class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded mr-2"
        >
          测试基础Viewer
        </button>
        
        <button 
          @click="clearLogs"
          class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded"
        >
          清除日志
        </button>
      </div>
      
      <!-- 日志显示区域 -->
      <div class="bg-black rounded p-4 h-96 overflow-y-auto">
        <div v-for="(log, index) in logs" :key="index" class="mb-1">
          <span :class="getLogClass(log.type)">[{{ log.time }}]</span>
          <span :class="getLogClass(log.type)">{{ log.message }}</span>
        </div>
      </div>
      
      <!-- Cesium容器 -->
      <div class="mt-4">
        <h3 class="text-lg font-semibold mb-2">Cesium容器</h3>
        <div 
          ref="cesiumContainer" 
          class="w-full h-64 bg-gray-800 rounded border"
          style="min-height: 300px;"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface LogEntry {
  type: 'info' | 'success' | 'warning' | 'error'
  message: string
  time: string
}

const logs = ref<LogEntry[]>([])
const cesiumContainer = ref<HTMLDivElement | null>(null)

let viewer: any = null

const addLog = (type: LogEntry['type'], message: string) => {
  logs.value.push({
    type,
    message,
    time: new Date().toLocaleTimeString()
  })
}

const getLogClass = (type: string) => {
  switch (type) {
    case 'success': return 'text-green-400'
    case 'warning': return 'text-yellow-400'
    case 'error': return 'text-red-400'
    default: return 'text-gray-300'
  }
}

const clearLogs = () => {
  logs.value = []
}

const testCesiumImport = async () => {
  try {
    addLog('info', '开始测试Cesium导入...')
    
    const Cesium = await import('cesium')
    addLog('success', 'Cesium模块导入成功')
    
    addLog('info', `Cesium版本: ${Cesium.VERSION || '未知'}`)
    addLog('info', `Cesium.Viewer: ${typeof Cesium.Viewer}`)
    addLog('info', `Cesium.Cartesian3: ${typeof Cesium.Cartesian3}`)
    addLog('info', `Cesium.Color: ${typeof Cesium.Color}`)
    
    addLog('success', 'Cesium基础类型检查通过')
    
  } catch (error: any) {
    addLog('error', `Cesium导入失败: ${error.message}`)
    console.error('Cesium导入错误:', error)
  }
}

const testBasicViewer = async () => {
  try {
    addLog('info', '开始测试Cesium Viewer创建...')
    
    if (!cesiumContainer.value) {
      addLog('error', 'Cesium容器未找到')
      return
    }
    
    // 清理现有viewer
    if (viewer) {
      addLog('info', '清理现有viewer')
      viewer.destroy()
      viewer = null
    }
    
    const Cesium = await import('cesium')
    addLog('success', 'Cesium模块加载成功')
    
    // 创建最简单的viewer
    addLog('info', '创建Cesium Viewer...')
    
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false,
      
      // 使用最简单的配置
      imageryProvider: new Cesium.OpenStreetMapImageryProvider({
        url: 'https://a.tile.openstreetmap.org/'
      })
    })
    
    addLog('success', 'Cesium Viewer创建成功')
    
    // 设置相机位置
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(113.5547, 22.2241, 100000)
    })
    
    addLog('success', '相机位置设置到珠海市')
    
    // 添加一个点
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(113.5547, 22.2241),
      point: {
        pixelSize: 10,
        color: Cesium.Color.YELLOW
      },
      label: {
        text: '珠海市',
        font: '14pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE
      }
    })
    
    addLog('success', '标记点添加成功')
    addLog('success', '🎉 Cesium测试完全成功！')
    
  } catch (error: any) {
    addLog('error', `Cesium Viewer创建失败: ${error.message}`)
    console.error('Cesium Viewer错误:', error)
  }
}

onMounted(() => {
  addLog('info', '组件已挂载，准备测试Cesium')
  addLog('info', '点击上方按钮开始测试')
})
</script>

<style scoped>
/* 确保容器有正确的尺寸 */
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* 隐藏Cesium的UI元素 */
:deep(.cesium-widget-credits) {
  display: none !important;
}

:deep(.cesium-viewer-bottom) {
  display: none !important;
}
</style>
