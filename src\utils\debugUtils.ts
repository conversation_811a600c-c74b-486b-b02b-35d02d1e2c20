// 调试工具函数
import { generateMockStations, generateMockWarnings } from '@/services/weatherService'
import { zhuhaiStationPositions, zhuhaiConfig } from '@/config/zhuhaiConfig'

// 验证数据完整性
export const validateData = () => {
  console.log('🔍 开始数据验证...')
  
  // 验证珠海配置
  console.log('珠海配置:', zhuhaiConfig)
  console.log('站点位置数量:', zhuhaiStationPositions.length)
  
  // 验证站点数据
  const stations = generateMockStations()
  console.log('生成的站点数量:', stations.length)
  
  stations.forEach((station, index) => {
    if (!station.position.longitude || !station.position.latitude) {
      console.error(`❌ 站点 ${index} 位置数据无效:`, station)
    } else {
      console.log(`✅ 站点 ${index} (${station.name}):`, {
        longitude: station.position.longitude,
        latitude: station.position.latitude,
        height: station.position.height
      })
    }
  })
  
  // 验证预警数据
  const warnings = generateMockWarnings()
  console.log('生成的预警数量:', warnings.length)
  
  warnings.forEach((warning, index) => {
    if (!warning.center.longitude || !warning.center.latitude) {
      console.error(`❌ 预警 ${index} 位置数据无效:`, warning)
    } else {
      console.log(`✅ 预警 ${index}:`, {
        type: warning.type,
        longitude: warning.center.longitude,
        latitude: warning.center.latitude,
        height: warning.center.height
      })
    }
  })
  
  console.log('🎉 数据验证完成!')
}

// 检查类型定义
export const checkTypes = () => {
  console.log('🔍 检查类型定义...')
  
  const testPosition = zhuhaiStationPositions[0]
  console.log('测试位置:', testPosition)
  console.log('经度类型:', typeof testPosition.longitude)
  console.log('纬度类型:', typeof testPosition.latitude)
  console.log('高度类型:', typeof testPosition.height)
  
  if (typeof testPosition.longitude !== 'number') {
    console.error('❌ 经度不是数字类型!')
  }
  if (typeof testPosition.latitude !== 'number') {
    console.error('❌ 纬度不是数字类型!')
  }
  
  console.log('✅ 类型检查完成!')
}

// 在开发环境自动运行
if (import.meta.env.DEV) {
  setTimeout(() => {
    validateData()
    checkTypes()
  }, 1000)
}
