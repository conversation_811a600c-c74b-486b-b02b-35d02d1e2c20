# 三维空域气象预警系统

基于Vue3 + TypeScript + Three.js的三维空域气象预警系统前端项目。

## 功能特性

### 🌍 三维场景
- 使用Three.js创建贴近现实的三维空域场景
- 包含地形、天空、云层等基础环境元素
- 支持视角旋转、缩放和平移操作
- 动态光照和阴影效果

### ⚠️ 气象预警功能
- **暴雨预警**：在三维场景中以蓝色区域和雨滴效果表示
- **狂风预警**：以旋转气流和沙尘效果表示
- **强风预警**：以气流轨迹和局部扰动效果表示
- **强降雨预警**：以密集雨滴和积水效果表示

### 🎮 交互功能
- 左侧控制面板：可切换不同预警类型的显示/隐藏
- 实时更新时间和预警数据
- 点击监测站点可查看详细信息
- 预警区域点击交互

### 🎨 视觉设计
- 使用Tailwind CSS实现现代化UI设计
- 添加平滑过渡和动画效果
- 预警区域使用不同颜色和透明度区分级别
- 响应式布局设计

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **3D渲染**: Three.js
- **样式框架**: Tailwind CSS
- **动画库**: GSAP
- **构建工具**: Vite
- **开发工具**: Vue DevTools

## 项目结构

```
src/
├── components/          # Vue组件
│   ├── ThreeScene.vue  # 三维场景组件
│   └── ControlPanel.vue # 控制面板组件
├── types/              # TypeScript类型定义
│   └── weather.ts      # 气象数据类型
├── config/             # 配置文件
│   └── weatherConfig.ts # 气象效果配置
├── services/           # 服务层
│   └── weatherService.ts # 气象数据服务
└── assets/             # 静态资源
```

## 快速开始

### 环境要求
- Node.js >= 16
- npm >= 8

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 类型检查
```bash
npm run type-check
```
