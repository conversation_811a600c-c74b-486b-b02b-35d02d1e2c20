# 珠海市三维气象预警系统

基于Vue3 + TypeScript + Cesium.js的三维地球气象预警系统，专注于珠海市气象监测与预警。

## 功能特性

### 🌍 三维地球场景
- 使用Cesium.js创建真实的三维地球场景
- 高精度地形和卫星影像
- 定位珠海市，包含香洲区、金湾区、斗门区
- 支持自由视角控制和地理导航

### ⚠️ 气象预警功能
- **暴雨预警**：蓝色椭圆区域标识，显示降雨强度
- **狂风预警**：黄色椭圆区域标识，显示风力等级
- **强风预警**：红色椭圆区域标识，显示风力影响范围
- **强降雨预警**：深蓝色椭圆区域标识，显示强降雨覆盖区域

### 🎮 交互功能
- **响应式控制按钮**：底部浮动控制台，支持移动端操作
- **实时监测站点**：珠海市10个真实位置的气象监测站
- **点击交互**：点击站点或预警区域查看详细信息
- **视图控制**：重置视图、显示/隐藏图层等功能

### 🎨 视觉设计
- 使用Tailwind CSS实现现代化UI设计
- 添加平滑过渡和动画效果
- 预警区域使用不同颜色和透明度区分级别
- 响应式布局设计

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **3D地球引擎**: Cesium.js
- **样式框架**: Tailwind CSS
- **动画库**: GSAP
- **构建工具**: Vite
- **地理数据**: 真实地理坐标和边界数据

## 项目结构

```
src/
├── components/              # Vue组件
│   ├── CesiumScene.vue     # Cesium三维地球场景组件
│   ├── ControlPanel.vue    # 详细控制面板组件
│   ├── WeatherControlButtons.vue # 响应式控制按钮
│   └── StatusIndicator.vue # 系统状态指示器
├── types/                  # TypeScript类型定义
│   └── weather.ts         # 气象数据类型（含地理坐标）
├── config/                 # 配置文件
│   ├── weatherConfig.ts   # 气象效果配置
│   └── zhuhaiConfig.ts    # 珠海市地理配置
├── services/              # 服务层
│   └── weatherService.ts  # 珠海市气象数据服务
└── utils/                 # 工具函数
    └── testUtils.ts       # 测试工具
```

## 快速开始

### 环境要求
- Node.js >= 16
- npm >= 8

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 类型检查
```bash
npm run type-check
```
