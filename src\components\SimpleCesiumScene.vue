<template>
  <div class="w-full h-full relative">
    <!-- Cesium容器 -->
    <div ref="cesiumContainer" class="w-full h-full"></div>
    
    <!-- 加载状态 -->
    <div 
      v-if="loading" 
      class="absolute inset-0 bg-black/50 flex items-center justify-center z-10"
    >
      <div class="bg-white rounded-lg p-6 text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-700">正在初始化Cesium...</p>
      </div>
    </div>
    
    <!-- 错误信息 -->
    <div 
      v-if="error" 
      class="absolute inset-0 bg-red-50 flex items-center justify-center z-10"
    >
      <div class="bg-white rounded-lg p-6 text-center max-w-md">
        <div class="text-red-600 text-4xl mb-4">⚠️</div>
        <h3 class="text-lg font-bold text-red-800 mb-2">Cesium加载失败</h3>
        <p class="text-red-600 text-sm mb-4">{{ error }}</p>
        <button 
          @click="retryInit"
          class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          重试
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'

// 响应式数据
const cesiumContainer = ref<HTMLDivElement | null>(null)
const loading = ref(true)
const error = ref('')

// Cesium相关变量
let viewer: any = null

// 初始化Cesium（简化版本）
const initCesium = async () => {
  try {
    loading.value = true
    error.value = ''
    
    console.log('开始初始化Cesium...')
    
    // 动态导入Cesium
    const Cesium = await import('cesium')
    console.log('Cesium模块加载成功')
    
    // 检查容器
    if (!cesiumContainer.value) {
      throw new Error('Cesium容器未找到')
    }
    
    console.log('创建Cesium Viewer...')
    
    // 创建最简单的Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      // 禁用所有不必要的UI
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false,
      navigationInstructionsInitiallyVisible: false,
      
      // 使用默认地形和影像
      terrainProvider: Cesium.createWorldTerrain({
        requestWaterMask: false,
        requestVertexNormals: false
      }),
      
      // 简单的影像提供者
      imageryProvider: new Cesium.OpenStreetMapImageryProvider({
        url: 'https://a.tile.openstreetmap.org/'
      })
    })
    
    console.log('Cesium Viewer创建成功')
    
    // 设置相机位置到珠海
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(113.5547, 22.2241, 50000),
      orientation: {
        heading: 0.0,
        pitch: -0.5,
        roll: 0.0
      }
    })
    
    console.log('相机位置设置完成')
    
    // 添加一个简单的标记
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(113.5547, 22.2241),
      point: {
        pixelSize: 10,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2
      },
      label: {
        text: '珠海市',
        font: '14pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -50)
      }
    })
    
    console.log('标记添加完成')
    
    loading.value = false
    console.log('Cesium初始化完成！')
    
  } catch (err: any) {
    console.error('Cesium初始化失败:', err)
    error.value = err.message || '未知错误'
    loading.value = false
  }
}

// 重试初始化
const retryInit = () => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
  initCesium()
}

// 生命周期
onMounted(() => {
  console.log('组件挂载，开始初始化Cesium')
  // 延迟一点时间确保DOM完全渲染
  setTimeout(initCesium, 100)
})

onBeforeUnmount(() => {
  console.log('组件卸载，清理Cesium')
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})

// 暴露方法
defineExpose({
  getViewer: () => viewer,
  retry: retryInit
})
</script>

<style scoped>
/* 确保容器有正确的尺寸 */
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* 隐藏Cesium的版权信息 */
:deep(.cesium-widget-credits) {
  display: none !important;
}

:deep(.cesium-viewer-bottom) {
  display: none !important;
}
</style>
