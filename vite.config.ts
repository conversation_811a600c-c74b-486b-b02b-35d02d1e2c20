import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
// import vueDevTools from 'vite-plugin-vue-devtools'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  define: {
    // 定义Cesium全局变量
    CESIUM_BASE_URL: JSON.stringify('/node_modules/cesium/Build/Cesium/')
  },
  build: {
    assetsInlineLimit: 0,
    chunkSizeWarningLimit: 1500,
    rollupOptions: {
      external: [],
      output: {
        manualChunks: {
          cesium: ['cesium']
        }
      }
    }
  },
  optimizeDeps: {
    include: ['cesium']
  },
  server: {
    fs: {
      allow: ['..']
    }
  }
})
