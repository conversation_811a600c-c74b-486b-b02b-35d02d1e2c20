// 气象预警类型枚举
export enum WeatherWarningType {
  HEAVY_RAIN = 'heavy_rain',      // 暴雨预警
  STRONG_WIND = 'strong_wind',    // 狂风预警
  GALE = 'gale',                  // 强风预警
  TORRENTIAL_RAIN = 'torrential_rain' // 强降雨预警
}

// 预警级别枚举
export enum WarningLevel {
  BLUE = 'blue',     // 蓝色预警
  YELLOW = 'yellow', // 黄色预警
  ORANGE = 'orange', // 橙色预警
  RED = 'red'        // 红色预警
}

// 地理坐标接口（经纬度）
export interface GeographicPosition {
  longitude: number  // 经度
  latitude: number   // 纬度
  height?: number    // 高度（米）
}

// 三维坐标接口（保留兼容性）
export interface Position3D {
  x: number
  y: number
  z: number
}

// 监测站点接口
export interface MonitoringStation {
  id: string
  name: string
  position: GeographicPosition  // 使用地理坐标
  altitude: number
  temperature: number
  humidity: number
  windSpeed: number
  windDirection: number
  pressure: number
  visibility: number
  lastUpdate: Date
  district?: string  // 所属区域
  stationType?: 'automatic' | 'manual' | 'radar'  // 站点类型
}

// 气象预警区域接口
export interface WeatherWarningArea {
  id: string
  type: WeatherWarningType
  level: WarningLevel
  center: GeographicPosition  // 使用地理坐标
  radius: number  // 半径（米）
  intensity: number // 强度 0-1
  startTime: Date
  endTime: Date
  description: string
  affectedStations: string[] // 受影响的监测站点ID
  polygon?: GeographicPosition[]  // 多边形区域（可选）
  district?: string  // 影响区域
}

// 粒子系统配置接口
export interface ParticleConfig {
  count: number
  size: number
  color: string
  opacity: number
  velocity: {
    x: number
    y: number
    z: number
  }
  lifespan: number
}

// 气象效果配置接口
export interface WeatherEffectConfig {
  [WeatherWarningType.HEAVY_RAIN]: {
    particles: ParticleConfig
    color: string
    intensity: number
  }
  [WeatherWarningType.STRONG_WIND]: {
    particles: ParticleConfig
    color: string
    intensity: number
    rotationSpeed: number
  }
  [WeatherWarningType.GALE]: {
    particles: ParticleConfig
    color: string
    intensity: number
    turbulence: number
  }
  [WeatherWarningType.TORRENTIAL_RAIN]: {
    particles: ParticleConfig
    color: string
    intensity: number
    density: number
  }
}

// 场景配置接口
export interface SceneConfig {
  terrain: {
    size: number
    segments: number
    heightVariation: number
  }
  lighting: {
    ambientIntensity: number
    directionalIntensity: number
    shadowMapSize: number
  }
  camera: {
    fov: number
    near: number
    far: number
    initialPosition: Position3D
  }
  fog: {
    color: string
    near: number
    far: number
  }
}

// 控制面板状态接口
export interface ControlPanelState {
  activeWarnings: Set<WeatherWarningType>
  selectedStation: string | null
  timeScale: number
  showStations: boolean
  showWarningAreas: boolean
  currentTime: Date
}

// 气象数据更新事件接口
export interface WeatherDataUpdate {
  stations: MonitoringStation[]
  warnings: WeatherWarningArea[]
  timestamp: Date
}

// Cesium场景配置接口
export interface CesiumSceneConfig {
  terrain: {
    provider: 'cesium' | 'arcgis' | 'ellipsoid'
    requestRenderMode: boolean
    enableLighting: boolean
  }
  camera: {
    destination: GeographicPosition
    orientation: {
      heading: number
      pitch: number
      roll: number
    }
  }
  imagery: {
    provider: 'osm' | 'arcgis' | 'bing' | 'google'
    alpha: number
  }
  atmosphere: {
    show: boolean
    hueShift: number
    saturationShift: number
    brightnessShift: number
  }
}

// 珠海市区域配置
export interface ZhuhaiConfig {
  center: GeographicPosition
  bounds: {
    west: number
    south: number
    east: number
    north: number
  }
  districts: {
    name: string
    center: GeographicPosition
    boundary: GeographicPosition[]
  }[]
}
