import type { WeatherWarningArea, MonitoringStation, GeographicPosition } from '@/types/weather'
import { WeatherWarningType, WarningLevel } from '@/types/weather'

// 气象预警数据服务
export class WeatherWarningService {
  private warnings: WeatherWarningArea[] = []
  private stations: MonitoringStation[] = []
  private updateCallbacks: Array<(warnings: WeatherWarningArea[], stations: MonitoringStation[]) => void> = []

  constructor() {
    this.initializeData()
  }

  // 初始化模拟数据
  private initializeData() {
    // 初始化监测站点
    this.stations = [
      {
        id: 'station-001',
        name: '香洲监测站',
        position: { longitude: 113.5547, latitude: 22.2241, height: 10 },
        altitude: 10,
        temperature: 28.5,
        humidity: 75,
        windSpeed: 12.3,
        windDirection: 135,
        pressure: 1013.2,
        visibility: 15.8,
        lastUpdate: new Date(),
        district: '香洲区',
        stationType: 'automatic'
      },
      {
        id: 'station-002',
        name: '吉大监测站',
        position: { longitude: 113.5800, latitude: 22.2500, height: 15 },
        altitude: 15,
        temperature: 27.8,
        humidity: 78,
        windSpeed: 15.6,
        windDirection: 120,
        pressure: 1012.8,
        visibility: 12.5,
        lastUpdate: new Date(),
        district: '香洲区',
        stationType: 'automatic'
      },
      {
        id: 'station-003',
        name: '金湾监测站',
        position: { longitude: 113.3200, latitude: 22.1300, height: 25 },
        altitude: 25,
        temperature: 29.2,
        humidity: 72,
        windSpeed: 18.9,
        windDirection: 150,
        pressure: 1011.5,
        visibility: 18.2,
        lastUpdate: new Date(),
        district: '金湾区',
        stationType: 'automatic'
      },
      {
        id: 'station-004',
        name: '斗门监测站',
        position: { longitude: 113.2100, latitude: 22.2100, height: 12 },
        altitude: 12,
        temperature: 26.9,
        humidity: 80,
        windSpeed: 8.7,
        windDirection: 90,
        pressure: 1014.1,
        visibility: 20.0,
        lastUpdate: new Date(),
        district: '斗门区',
        stationType: 'automatic'
      }
    ]

    // 初始化预警区域
    this.warnings = [
      {
        id: 'warning-001',
        type: WeatherWarningType.HEAVY_RAIN,
        level: WarningLevel.YELLOW,
        center: { longitude: 113.4500, latitude: 22.2000, height: 0 },
        radius: 8000,
        intensity: 0.7,
        startTime: new Date(),
        endTime: new Date(Date.now() + 6 * 60 * 60 * 1000), // 6小时后
        description: '预计未来6小时内，金湾区将出现50-80mm降雨',
        affectedStations: ['station-003'],
        isActive: true
      },
      {
        id: 'warning-002',
        type: WeatherWarningType.STRONG_WIND,
        level: WarningLevel.ORANGE,
        center: { longitude: 113.5800, latitude: 22.2500, height: 0 },
        radius: 12000,
        intensity: 0.85,
        startTime: new Date(),
        endTime: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4小时后
        description: '预计未来4小时内，香洲区东部将出现8-10级大风',
        affectedStations: ['station-001', 'station-002'],
        isActive: true
      }
    ]
  }

  // 获取所有预警
  getWarnings(): WeatherWarningArea[] {
    return [...this.warnings]
  }

  // 获取活跃预警
  getActiveWarnings(): WeatherWarningArea[] {
    return this.warnings.filter(w => w.isActive)
  }

  // 获取所有监测站点
  getStations(): MonitoringStation[] {
    return [...this.stations]
  }

  // 根据类型获取预警
  getWarningsByType(type: WeatherWarningType): WeatherWarningArea[] {
    return this.warnings.filter(w => w.type === type && w.isActive)
  }

  // 添加预警
  addWarning(warning: Omit<WeatherWarningArea, 'id'>): string {
    const id = `warning-${Date.now()}`
    const newWarning: WeatherWarningArea = {
      ...warning,
      id
    }
    this.warnings.push(newWarning)
    this.notifyUpdate()
    return id
  }

  // 移除预警
  removeWarning(id: string): boolean {
    const index = this.warnings.findIndex(w => w.id === id)
    if (index !== -1) {
      this.warnings.splice(index, 1)
      this.notifyUpdate()
      return true
    }
    return false
  }

  // 切换预警状态
  toggleWarning(id: string): boolean {
    const warning = this.warnings.find(w => w.id === id)
    if (warning) {
      warning.isActive = !warning.isActive
      this.notifyUpdate()
      return true
    }
    return false
  }

  // 更新站点数据
  updateStationData(stationId: string, data: Partial<MonitoringStation>): boolean {
    const station = this.stations.find(s => s.id === stationId)
    if (station) {
      Object.assign(station, data, { lastUpdate: new Date() })
      this.notifyUpdate()
      return true
    }
    return false
  }

  // 模拟实时数据更新
  startRealTimeUpdates() {
    setInterval(() => {
      // 随机更新站点数据
      this.stations.forEach(station => {
        station.temperature += (Math.random() - 0.5) * 2
        station.humidity += (Math.random() - 0.5) * 5
        station.windSpeed += (Math.random() - 0.5) * 3
        station.pressure += (Math.random() - 0.5) * 2
        station.lastUpdate = new Date()
      })
      this.notifyUpdate()
    }, 30000) // 每30秒更新一次
  }

  // 注册更新回调
  onUpdate(callback: (warnings: WeatherWarningArea[], stations: MonitoringStation[]) => void) {
    this.updateCallbacks.push(callback)
  }

  // 通知更新
  private notifyUpdate() {
    this.updateCallbacks.forEach(callback => {
      callback([...this.warnings], [...this.stations])
    })
  }

  // 获取预警统计
  getWarningStats() {
    const active = this.warnings.filter(w => w.isActive)
    return {
      total: this.warnings.length,
      active: active.length,
      byType: {
        [WeatherWarningType.HEAVY_RAIN]: active.filter(w => w.type === WeatherWarningType.HEAVY_RAIN).length,
        [WeatherWarningType.STRONG_WIND]: active.filter(w => w.type === WeatherWarningType.STRONG_WIND).length,
        [WeatherWarningType.GALE]: active.filter(w => w.type === WeatherWarningType.GALE).length,
        [WeatherWarningType.TORRENTIAL_RAIN]: active.filter(w => w.type === WeatherWarningType.TORRENTIAL_RAIN).length
      },
      byLevel: {
        [WarningLevel.BLUE]: active.filter(w => w.level === WarningLevel.BLUE).length,
        [WarningLevel.YELLOW]: active.filter(w => w.level === WarningLevel.YELLOW).length,
        [WarningLevel.ORANGE]: active.filter(w => w.level === WarningLevel.ORANGE).length,
        [WarningLevel.RED]: active.filter(w => w.level === WarningLevel.RED).length
      }
    }
  }
}

// 单例实例
export const weatherWarningService = new WeatherWarningService()
