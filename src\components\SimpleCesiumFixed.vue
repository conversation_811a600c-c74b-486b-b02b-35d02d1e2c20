<template>
  <div class="w-full h-full relative">
    <!-- Cesium容器 -->
    <div ref="cesiumContainer" class="w-full h-full"></div>
    
    <!-- 加载指示器 -->
    <div 
      v-if="loading" 
      class="absolute inset-0 bg-black/50 flex items-center justify-center z-10"
    >
      <div class="bg-white rounded-lg p-6 text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-700">正在初始化Cesium地球...</p>
      </div>
    </div>
    
    <!-- 错误信息 -->
    <div 
      v-if="error" 
      class="absolute inset-0 bg-red-50 flex items-center justify-center z-10"
    >
      <div class="bg-white rounded-lg p-6 text-center max-w-md">
        <div class="text-red-600 text-4xl mb-4">⚠️</div>
        <h3 class="text-lg font-bold text-red-800 mb-2">Cesium加载失败</h3>
        <p class="text-red-600 text-sm mb-4">{{ error }}</p>
        <button 
          @click="retryInit"
          class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          重试
        </button>
      </div>
    </div>

    <!-- 信息面板 -->
    <div 
      v-if="selectedEntity" 
      class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg max-w-sm z-20"
    >
      <h3 class="font-bold text-lg mb-2">{{ selectedEntity.name }}</h3>
      <div class="space-y-1 text-sm">
        <p><strong>类型:</strong> {{ selectedEntity.type }}</p>
        <p><strong>位置:</strong> {{ selectedEntity.position }}</p>
      </div>
      <button 
        @click="selectedEntity = null"
        class="mt-3 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
      >
        关闭
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'

// 响应式数据
const cesiumContainer = ref<HTMLDivElement | null>(null)
const loading = ref(true)
const error = ref('')
const selectedEntity = ref<any>(null)

// Cesium相关变量
let viewer: any = null

// 珠海市中心坐标
const ZHUHAI_CENTER = {
  longitude: 113.5547,
  latitude: 22.2241,
  height: 50000
}

// 初始化Cesium（简化版本）
const initCesium = async () => {
  try {
    loading.value = true
    error.value = ''
    
    console.log('开始初始化Cesium...')
    
    // 动态导入Cesium
    const Cesium = await import('cesium')
    console.log('Cesium模块加载成功')
    
    // 检查容器
    if (!cesiumContainer.value) {
      throw new Error('Cesium容器未找到')
    }
    
    console.log('创建Cesium Viewer...')
    
    // 创建最简单的Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      // 禁用所有不必要的UI
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false,
      navigationInstructionsInitiallyVisible: false,
      requestRenderMode: true,
      maximumRenderTimeChange: Infinity
    })
    
    console.log('Cesium Viewer创建成功')
    
    // 设置相机位置到珠海
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        ZHUHAI_CENTER.longitude,
        ZHUHAI_CENTER.latitude,
        ZHUHAI_CENTER.height
      ),
      orientation: {
        heading: 0.0,
        pitch: -0.5,
        roll: 0.0
      }
    })
    
    console.log('相机位置设置完成')
    
    // 添加珠海市标记
    const zhuhaiEntity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(113.5547, 22.2241),
      point: {
        pixelSize: 15,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2
      },
      label: {
        text: '珠海市',
        font: '16pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -50)
      }
    })
    
    // 添加一些示例监测站点
    const stations = [
      { name: '香洲站', lon: 113.5547, lat: 22.2241 },
      { name: '金湾站', lon: 113.3200, lat: 22.1300 },
      { name: '斗门站', lon: 113.2100, lat: 22.2100 }
    ]
    
    stations.forEach(station => {
      const entity = viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(station.lon, station.lat),
        point: {
          pixelSize: 10,
          color: Cesium.Color.CYAN,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2
        },
        label: {
          text: station.name,
          font: '12pt sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 1,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -30)
        }
      })
      
      // 添加自定义数据
      ;(entity as any).customData = {
        type: '监测站点',
        name: station.name,
        position: `${station.lon.toFixed(4)}, ${station.lat.toFixed(4)}`
      }
    })
    
    // 添加点击事件
    viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(
      (event: any) => {
        const pickedObject = viewer.scene.pick(event.position)
        if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.id)) {
          const entity = pickedObject.id as any
          if (entity.customData) {
            selectedEntity.value = entity.customData
          }
        }
      },
      Cesium.ScreenSpaceEventType.LEFT_CLICK
    )
    
    console.log('标记添加完成')
    
    loading.value = false
    console.log('Cesium初始化完成！')
    
  } catch (err: any) {
    console.error('Cesium初始化失败:', err)
    error.value = err.message || '未知错误'
    loading.value = false
  }
}

// 重试初始化
const retryInit = () => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
  initCesium()
}

// 生命周期
onMounted(() => {
  console.log('组件挂载，开始初始化Cesium')
  // 延迟一点时间确保DOM完全渲染
  setTimeout(initCesium, 100)
})

onBeforeUnmount(() => {
  console.log('组件卸载，清理Cesium')
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})

// 暴露方法
defineExpose({
  getViewer: () => viewer,
  retry: retryInit,
  flyToZhuhai: () => {
    if (viewer) {
      const Cesium = (window as any).Cesium
      viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
          ZHUHAI_CENTER.longitude,
          ZHUHAI_CENTER.latitude,
          ZHUHAI_CENTER.height
        )
      })
    }
  }
})
</script>

<style scoped>
/* 确保容器有正确的尺寸 */
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* 隐藏Cesium的版权信息 */
:deep(.cesium-widget-credits) {
  display: none !important;
}

:deep(.cesium-viewer-bottom) {
  display: none !important;
}

/* 加载动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
