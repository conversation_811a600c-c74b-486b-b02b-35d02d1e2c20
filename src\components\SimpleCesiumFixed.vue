<template>
  <div class="cesium-wrapper">
    <!-- Cesium容器 -->
    <div ref="cesiumContainer" class="cesium-container"></div>
    
    <!-- 加载指示器 -->
    <div 
      v-if="loading" 
      class="absolute inset-0 bg-black/50 flex items-center justify-center z-10"
    >
      <div class="bg-white rounded-lg p-6 text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-700">正在初始化Cesium地球...</p>
      </div>
    </div>
    
    <!-- 错误信息 -->
    <div 
      v-if="error" 
      class="absolute inset-0 bg-red-50 flex items-center justify-center z-10"
    >
      <div class="bg-white rounded-lg p-6 text-center max-w-md">
        <div class="text-red-600 text-4xl mb-4">⚠️</div>
        <h3 class="text-lg font-bold text-red-800 mb-2">Cesium加载失败</h3>
        <p class="text-red-600 text-sm mb-4">{{ error }}</p>
        <button 
          @click="retryInit"
          class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          重试
        </button>
      </div>
    </div>

    <!-- 信息面板 -->
    <div 
      v-if="selectedEntity" 
      class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg max-w-sm z-20"
    >
      <h3 class="font-bold text-lg mb-2">{{ selectedEntity.name }}</h3>
      <div class="space-y-1 text-sm">
        <p><strong>类型:</strong> {{ selectedEntity.type }}</p>
        <p><strong>位置:</strong> {{ selectedEntity.position }}</p>
      </div>
      <button 
        @click="selectedEntity = null"
        class="mt-3 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
      >
        关闭
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'

// 响应式数据
const cesiumContainer = ref<HTMLDivElement | null>(null)
const loading = ref(true)
const error = ref('')
const selectedEntity = ref<any>(null)

// Cesium相关变量
let viewer: any = null

// 珠海市中心坐标
const ZHUHAI_CENTER = {
  longitude: 113.5547,
  latitude: 22.2241,
  height: 50000
}

// 初始化Cesium（简化版本）
const initCesium = async () => {
  try {
    loading.value = true
    error.value = ''
    
    console.log('开始初始化Cesium...')
    
    // 动态导入Cesium
    const Cesium = await import('cesium')
    console.log('Cesium模块加载成功')
    
    // 检查容器
    if (!cesiumContainer.value) {
      throw new Error('Cesium容器未找到')
    }
    
    console.log('创建Cesium Viewer...')
    
    // 创建全屏Cesium Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      // 禁用所有UI控件，实现纯净的地球视图
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false,
      navigationInstructionsInitiallyVisible: false,
      creditContainer: undefined, // 隐藏版权信息
      requestRenderMode: false, // 连续渲染以获得更好的性能
      maximumRenderTimeChange: Infinity
    })

    // 确保Cesium容器填充整个屏幕
    const cesiumWidget = viewer.cesiumWidget
    cesiumWidget.canvas.style.width = '100%'
    cesiumWidget.canvas.style.height = '100%'
    cesiumWidget.canvas.style.display = 'block'
    
    console.log('Cesium Viewer创建成功')
    
    // 设置相机位置到珠海
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        ZHUHAI_CENTER.longitude,
        ZHUHAI_CENTER.latitude,
        ZHUHAI_CENTER.height
      ),
      orientation: {
        heading: 0.0,
        pitch: -0.5,
        roll: 0.0
      }
    })
    
    console.log('相机位置设置完成')
    
    // 添加珠海市标记
    const zhuhaiEntity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(113.5547, 22.2241),
      point: {
        pixelSize: 15,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2
      },
      label: {
        text: '珠海市',
        font: '16pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -50)
      }
    })
    
    // 添加一些示例监测站点
    const stations = [
      { name: '香洲站', lon: 113.5547, lat: 22.2241 },
      { name: '金湾站', lon: 113.3200, lat: 22.1300 },
      { name: '斗门站', lon: 113.2100, lat: 22.2100 }
    ]
    
    stations.forEach(station => {
      const entity = viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(station.lon, station.lat),
        point: {
          pixelSize: 10,
          color: Cesium.Color.CYAN,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2
        },
        label: {
          text: station.name,
          font: '12pt sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 1,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -30)
        }
      })
      
      // 添加自定义数据
      ;(entity as any).customData = {
        type: '监测站点',
        name: station.name,
        position: `${station.lon.toFixed(4)}, ${station.lat.toFixed(4)}`
      }
    })
    
    // 添加点击事件
    viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(
      (event: any) => {
        const pickedObject = viewer.scene.pick(event.position)
        if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.id)) {
          const entity = pickedObject.id as any
          if (entity.customData) {
            selectedEntity.value = entity.customData
          }
        }
      },
      Cesium.ScreenSpaceEventType.LEFT_CLICK
    )
    
    console.log('标记添加完成')

    // 添加窗口大小变化监听
    const handleResize = () => {
      if (viewer && viewer.cesiumWidget) {
        viewer.cesiumWidget.resize()
      }
    }

    window.addEventListener('resize', handleResize)

    // 强制触发一次resize确保正确显示
    setTimeout(() => {
      handleResize()
    }, 100)

    loading.value = false
    console.log('Cesium初始化完成！')
    
  } catch (err: any) {
    console.error('Cesium初始化失败:', err)
    error.value = err.message || '未知错误'
    loading.value = false
  }
}

// 重试初始化
const retryInit = () => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
  initCesium()
}

// 生命周期
onMounted(() => {
  console.log('组件挂载，开始初始化Cesium')
  // 延迟一点时间确保DOM完全渲染
  setTimeout(initCesium, 100)
})

onBeforeUnmount(() => {
  console.log('组件卸载，清理Cesium')
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})

// 暴露方法
defineExpose({
  getViewer: () => viewer,
  retry: retryInit,
  flyToZhuhai: () => {
    if (viewer) {
      const Cesium = (window as any).Cesium
      viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
          ZHUHAI_CENTER.longitude,
          ZHUHAI_CENTER.latitude,
          ZHUHAI_CENTER.height
        )
      })
    }
  }
})
</script>

<style scoped>
/* 全屏容器样式 */
.cesium-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  z-index: 1;
}

.cesium-container {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
}

/* 确保加载和错误提示在最上层 */
.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

/* 布局样式 */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-center {
  text-align: center;
}

/* 背景和颜色 */
.bg-black\/50 {
  background-color: rgba(0, 0, 0, 0.5);
}

.bg-white {
  background-color: white;
}

.bg-red-50 {
  background-color: #fef2f2;
}

.bg-white\/90 {
  background-color: rgba(255, 255, 255, 0.9);
}

/* 文字颜色 */
.text-gray-700 {
  color: #374151;
}

.text-red-600 {
  color: #dc2626;
}

.text-red-800 {
  color: #991b1b;
}

.text-white {
  color: white;
}

/* 字体大小和粗细 */
.text-lg {
  font-size: 1.125rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-4xl {
  font-size: 2.25rem;
}

.font-bold {
  font-weight: 700;
}

/* 间距 */
.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

/* 尺寸 */
.h-12 {
  height: 3rem;
}

.w-12 {
  width: 3rem;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-md {
  max-width: 28rem;
}

/* 边框和圆角 */
.rounded {
  border-radius: 0.25rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-blue-600 {
  border-color: #2563eb;
}

/* 阴影和模糊 */
.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* 按钮样式 */
.bg-blue-600 {
  background-color: #2563eb;
}

.hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

button {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

/* 定位 */
.top-4 {
  top: 1rem;
}

.right-4 {
  right: 1rem;
}

/* 间距 */
.space-y-1 > * + * {
  margin-top: 0.25rem;
}

/* 隐藏Cesium的版权信息 */
:deep(.cesium-widget-credits) {
  display: none !important;
}

:deep(.cesium-viewer-bottom) {
  display: none !important;
}

/* 隐藏Cesium的工具栏 */
:deep(.cesium-viewer-toolbar) {
  display: none !important;
}

/* 加载动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
