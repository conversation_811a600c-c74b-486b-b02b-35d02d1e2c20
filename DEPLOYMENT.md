# 珠海市三维气象预警系统部署指南

## 系统要求

### 开发环境
- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0
- 现代浏览器（Chrome 88+, Firefox 85+, Safari 14+, Edge 88+）

### 生产环境
- Web服务器（Nginx, Apache, 或 CDN）
- HTTPS支持（Cesium需要安全上下文）
- 足够的带宽（Cesium资源较大）

## 本地开发

### 1. 克隆项目
```bash
git clone <repository-url>
cd xiangmukaifa
```

### 2. 安装依赖
```bash
npm install
# 或
yarn install
```

### 3. 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

### 4. 访问应用
打开浏览器访问 `http://localhost:5173`

## 生产构建

### 1. 构建项目
```bash
npm run build
# 或
yarn build
```

### 2. 预览构建结果
```bash
npm run preview
# 或
yarn preview
```

## 部署配置

### Nginx配置示例
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # 静态文件目录
    root /path/to/dist;
    index index.html;
    
    # 支持Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Cesium资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
}
```

### Apache配置示例
```apache
<VirtualHost *:443>
    ServerName your-domain.com
    DocumentRoot /path/to/dist
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    # 支持Vue Router的history模式
    <Directory "/path/to/dist">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # 缓存配置
    <LocationMatch "\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </LocationMatch>
</VirtualHost>
```

## 环境变量配置

### 开发环境 (.env.development)
```env
VITE_APP_TITLE=珠海市气象预警系统
VITE_CESIUM_ACCESS_TOKEN=your-cesium-access-token
VITE_API_BASE_URL=http://localhost:3000/api
VITE_WEATHER_UPDATE_INTERVAL=5000
```

### 生产环境 (.env.production)
```env
VITE_APP_TITLE=珠海市气象预警系统
VITE_CESIUM_ACCESS_TOKEN=your-production-cesium-token
VITE_API_BASE_URL=https://api.your-domain.com
VITE_WEATHER_UPDATE_INTERVAL=10000
```

## 性能优化

### 1. Cesium优化
- 使用CDN加载Cesium资源
- 启用请求渲染模式
- 合理设置地形和影像质量

### 2. 代码分割
```typescript
// 动态导入大型组件
const CesiumScene = defineAsyncComponent(() => import('@/components/CesiumScene.vue'))
```

### 3. 资源压缩
- 启用Gzip/Brotli压缩
- 优化图片资源
- 使用WebP格式图片

### 4. 缓存策略
- 静态资源长期缓存
- API数据适当缓存
- 使用Service Worker

## 监控和日志

### 1. 错误监控
```typescript
// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error)
  // 发送到监控服务
})

// Vue错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue error:', err, info)
  // 发送到监控服务
}
```

### 2. 性能监控
```typescript
// 性能指标收集
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    console.log('Performance:', entry.name, entry.duration)
  }
})
observer.observe({ entryTypes: ['measure', 'navigation'] })
```

## 安全配置

### 1. CSP (Content Security Policy)
```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-eval' https://cesium.com; 
               style-src 'self' 'unsafe-inline' https://cesium.com;
               img-src 'self' data: https:;
               connect-src 'self' https:;">
```

### 2. HTTPS强制
```nginx
# 重定向HTTP到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## 故障排除

### 常见问题

1. **Cesium加载失败**
   - 检查网络连接
   - 验证访问令牌
   - 确保HTTPS环境

2. **性能问题**
   - 检查设备GPU支持
   - 降低地形质量
   - 减少同时显示的实体数量

3. **移动端兼容性**
   - 检查WebGL支持
   - 优化触摸交互
   - 调整UI尺寸

### 调试工具
- Chrome DevTools
- Cesium Inspector
- Vue DevTools
- 网络监控工具

## 更新和维护

### 1. 依赖更新
```bash
# 检查过时的依赖
npm outdated

# 更新依赖
npm update
```

### 2. 安全更新
```bash
# 安全审计
npm audit

# 修复安全问题
npm audit fix
```

### 3. 备份策略
- 定期备份源代码
- 备份配置文件
- 备份数据库（如有）

## 技术支持

如需技术支持，请联系：
- 邮箱: <EMAIL>
- 文档: https://docs.your-domain.com
- 问题反馈: https://github.com/your-repo/issues
