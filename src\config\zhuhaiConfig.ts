import type { ZhuhaiConfig, CesiumSceneConfig, GeographicPosition } from '@/types/weather'

// 珠海市中心坐标（拱北口岸附近）
export const ZHUHAI_CENTER: GeographicPosition = {
  longitude: 113.5547,
  latitude: 22.2241,
  height: 1000
}

// 珠海市边界
export const Z<PERSON><PERSON><PERSON>I_BOUNDS = {
  west: 113.0,
  south: 21.8,
  east: 114.2,
  north: 22.6
}

// 珠海市各区配置
export const zhuhaiConfig: ZhuhaiConfig = {
  center: ZHUHAI_CENTER,
  bounds: ZHUHAI_BOUNDS,
  districts: [
    {
      name: '香洲区',
      center: { longitude: 113.5547, latitude: 22.2241, height: 0 },
      boundary: [
        { longitude: 113.4, latitude: 22.1, height: 0 },
        { longitude: 113.7, latitude: 22.1, height: 0 },
        { longitude: 113.7, latitude: 22.4, height: 0 },
        { longitude: 113.4, latitude: 22.4, height: 0 }
      ]
    },
    {
      name: '金湾区',
      center: { longitude: 113.3, latitude: 22.1, height: 0 },
      boundary: [
        { longitude: 113.1, latitude: 21.9, height: 0 },
        { longitude: 113.5, latitude: 21.9, height: 0 },
        { longitude: 113.5, latitude: 22.3, height: 0 },
        { longitude: 113.1, latitude: 22.3, height: 0 }
      ]
    },
    {
      name: '斗门区',
      center: { longitude: 113.2, latitude: 22.2, height: 0 },
      boundary: [
        { longitude: 113.0, latitude: 22.0, height: 0 },
        { longitude: 113.4, latitude: 22.0, height: 0 },
        { longitude: 113.4, latitude: 22.4, height: 0 },
        { longitude: 113.0, latitude: 22.4, height: 0 }
      ]
    }
  ]
}

// Cesium场景默认配置
export const defaultCesiumConfig: CesiumSceneConfig = {
  terrain: {
    provider: 'cesium',
    requestRenderMode: true,
    enableLighting: true
  },
  camera: {
    destination: {
      longitude: ZHUHAI_CENTER.longitude,
      latitude: ZHUHAI_CENTER.latitude,
      height: 50000  // 50公里高度俯视
    },
    orientation: {
      heading: 0.0,
      pitch: -0.5,  // 向下倾斜
      roll: 0.0
    }
  },
  imagery: {
    provider: 'arcgis',
    alpha: 1.0
  },
  atmosphere: {
    show: true,
    hueShift: 0.0,
    saturationShift: 0.0,
    brightnessShift: 0.0
  }
}

// 珠海市监测站点位置（真实位置参考）
export const zhuhaiStationPositions: GeographicPosition[] = [
  // 香洲区
  { longitude: 113.5547, latitude: 22.2241, height: 10 }, // 拱北
  { longitude: 113.5800, latitude: 22.2500, height: 15 }, // 吉大
  { longitude: 113.5200, latitude: 22.2700, height: 20 }, // 前山
  { longitude: 113.5900, latitude: 22.2200, height: 5 },  // 湾仔
  
  // 金湾区
  { longitude: 113.3200, latitude: 22.1300, height: 25 }, // 三灶
  { longitude: 113.3800, latitude: 22.0800, height: 30 }, // 红旗
  { longitude: 113.2800, latitude: 22.1800, height: 18 }, // 平沙
  
  // 斗门区
  { longitude: 113.2100, latitude: 22.2100, height: 12 }, // 井岸
  { longitude: 113.1800, latitude: 22.1800, height: 8 },  // 白蕉
  { longitude: 113.2400, latitude: 22.2400, height: 22 }, // 斗门
]

// 气象预警区域颜色配置（适配Cesium）
export const cesiumWarningColors = {
  heavy_rain: {
    color: 'rgba(30, 64, 175, 0.6)',    // 蓝色
    outlineColor: 'rgba(30, 64, 175, 1.0)'
  },
  strong_wind: {
    color: 'rgba(245, 158, 11, 0.6)',   // 黄色
    outlineColor: 'rgba(245, 158, 11, 1.0)'
  },
  gale: {
    color: 'rgba(220, 38, 38, 0.6)',    // 红色
    outlineColor: 'rgba(220, 38, 38, 1.0)'
  },
  torrential_rain: {
    color: 'rgba(30, 58, 138, 0.8)',    // 深蓝色
    outlineColor: 'rgba(30, 58, 138, 1.0)'
  }
}

// 监测站点图标配置
export const stationIconConfig = {
  normal: {
    image: '/icons/weather-station.png',
    scale: 1.0,
    color: 'rgba(16, 185, 129, 1.0)'  // 绿色
  },
  warning: {
    image: '/icons/weather-station-warning.png',
    scale: 1.2,
    color: 'rgba(239, 68, 68, 1.0)'   // 红色
  },
  selected: {
    image: '/icons/weather-station-selected.png',
    scale: 1.5,
    color: 'rgba(59, 130, 246, 1.0)'  // 蓝色
  }
}

// 天气效果配置
export const weatherEffectConfig = {
  rain: {
    particleSize: 2.0,
    speed: 50.0,
    density: 1000,
    color: 'rgba(100, 149, 237, 0.8)'
  },
  wind: {
    particleSize: 1.5,
    speed: 30.0,
    density: 500,
    color: 'rgba(255, 215, 0, 0.6)'
  },
  storm: {
    particleSize: 3.0,
    speed: 80.0,
    density: 1500,
    color: 'rgba(70, 130, 180, 0.9)'
  }
}
