<template>
  <div class="app-container">
    <div class="content">
      <!-- 标题 -->
      <div class="header">
        <h1 class="title">🌍 珠海市气象预警系统</h1>
        <p class="subtitle">基于Vue3 + TypeScript的现代化气象监测平台</p>
      </div>

      <!-- 状态卡片 -->
      <div class="cards-grid">
        <div class="card">
          <div class="card-icon">🌧️</div>
          <h3 class="card-title">暴雨预警</h3>
          <p class="card-text">当前状态: {{ rainWarning ? '已激活' : '未激活' }}</p>
          <button 
            @click="toggleRain"
            :class="['btn', rainWarning ? 'btn-active' : 'btn-inactive']"
          >
            {{ rainWarning ? '关闭预警' : '开启预警' }}
          </button>
        </div>

        <div class="card">
          <div class="card-icon">💨</div>
          <h3 class="card-title">风力预警</h3>
          <p class="card-text">当前状态: {{ windWarning ? '已激活' : '未激活' }}</p>
          <button 
            @click="toggleWind"
            :class="['btn', windWarning ? 'btn-active' : 'btn-inactive']"
          >
            {{ windWarning ? '关闭预警' : '开启预警' }}
          </button>
        </div>

        <div class="card">
          <div class="card-icon">📍</div>
          <h3 class="card-title">监测站点</h3>
          <p class="card-text">在线站点: {{ stationCount }} 个</p>
          <button @click="refreshStations" class="btn btn-success">
            刷新数据
          </button>
        </div>
      </div>

      <!-- 珠海市区域信息 -->
      <div class="info-section">
        <h2 class="section-title">📍 珠海市行政区划</h2>
        <div class="districts-grid">
          <div class="district">
            <h3 class="district-title district-xiangzhou">香洲区</h3>
            <p class="district-desc">主城区，包含拱北口岸、吉大、前山等地</p>
            <div class="district-stats">监测站点: 4个</div>
          </div>
          <div class="district">
            <h3 class="district-title district-jinwan">金湾区</h3>
            <p class="district-desc">西部区域，包含三灶、红旗、平沙等地</p>
            <div class="district-stats">监测站点: 3个</div>
          </div>
          <div class="district">
            <h3 class="district-title district-doumen">斗门区</h3>
            <p class="district-desc">北部区域，包含井岸、白蕉、斗门等地</p>
            <div class="district-stats">监测站点: 3个</div>
          </div>
        </div>
      </div>

      <!-- 系统日志 -->
      <div class="log-section">
        <h3 class="log-title">📋 系统日志</h3>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-entry">
            <span class="log-time">[{{ log.time }}]</span>
            <span :class="['log-message', `log-${log.type}`]">{{ log.message }}</span>
          </div>
          <div v-if="logs.length === 0" class="log-empty">
            暂无日志记录...
          </div>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="footer">
        <p>珠海市三维气象预警系统 v2.0</p>
        <p>基于 Vue 3 + TypeScript</p>
        <p>当前时间: {{ currentTime }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'

// 响应式数据
const rainWarning = ref(false)
const windWarning = ref(false)
const stationCount = ref(10)
const currentTime = ref('')
const logs = ref<Array<{type: string, message: string, time: string}>>([])

// 方法
const toggleRain = () => {
  rainWarning.value = !rainWarning.value
  addLog('info', `暴雨预警已${rainWarning.value ? '开启' : '关闭'}`)
}

const toggleWind = () => {
  windWarning.value = !windWarning.value
  addLog('info', `风力预警已${windWarning.value ? '开启' : '关闭'}`)
}

const refreshStations = () => {
  addLog('success', '正在刷新监测站点数据...')
  setTimeout(() => {
    stationCount.value = Math.floor(Math.random() * 3) + 8 // 8-10个站点
    addLog('success', `数据刷新完成，当前在线站点: ${stationCount.value}个`)
  }, 1000)
}

const addLog = (type: string, message: string) => {
  logs.value.unshift({
    type,
    message,
    time: new Date().toLocaleTimeString()
  })
  
  // 保持最多10条日志
  if (logs.value.length > 10) {
    logs.value = logs.value.slice(0, 10)
  }
}

const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

// 生命周期
let timeInterval: number | null = null

onMounted(() => {
  addLog('success', '珠海市气象预警系统启动成功')
  addLog('info', '正在初始化监测站点...')
  
  setTimeout(() => {
    addLog('success', '所有监测站点连接正常')
  }, 1500)
  
  updateTime()
  timeInterval = window.setInterval(updateTime, 1000)
})

onBeforeUnmount(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 主容器 */
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3a8a 0%, #7c3aed 100%);
  color: white;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  padding: 2rem;
}

.content {
  max-width: 1200px;
  margin: 0 auto;
}

/* 标题区域 */
.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.8;
}

/* 卡片网格 */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.2s ease;
}

.card:hover {
  transform: translateY(-5px);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.card-text {
  opacity: 0.8;
  margin-bottom: 1rem;
}

/* 按钮样式 */
.btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.btn:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

.btn-active {
  background: #ef4444;
}

.btn-inactive {
  background: #6b7280;
}

.btn-success {
  background: #10b981;
}

/* 信息区域 */
.info-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 2rem;
  text-align: center;
}

.districts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.district {
  text-align: center;
  padding: 1rem;
}

.district-title {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.district-xiangzhou {
  color: #10b981;
}

.district-jinwan {
  color: #3b82f6;
}

.district-doumen {
  color: #8b5cf6;
}

.district-desc {
  opacity: 0.8;
  font-size: 0.9rem;
}

.district-stats {
  margin-top: 0.5rem;
  font-size: 0.8rem;
  opacity: 0.7;
}

/* 日志区域 */
.log-section {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.log-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.log-container {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 0.5rem;
  padding: 1rem;
  max-height: 200px;
  overflow-y: auto;
}

.log-entry {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.log-time {
  color: #6b7280;
}

.log-message {
  margin-left: 0.5rem;
}

.log-success {
  color: #10b981;
}

.log-warning {
  color: #f59e0b;
}

.log-error {
  color: #ef4444;
}

.log-info {
  color: #3b82f6;
}

.log-empty {
  color: #6b7280;
  font-style: italic;
}

/* 底部 */
.footer {
  text-align: center;
  margin-top: 3rem;
  opacity: 0.6;
  font-size: 0.9rem;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 1rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .cards-grid {
    grid-template-columns: 1fr;
  }
  
  .districts-grid {
    grid-template-columns: 1fr;
  }
}
</style>
