import { WeatherWarningType, WeatherEffectConfig, SceneConfig } from '@/types/weather'

// 默认气象效果配置
export const defaultWeatherEffects: WeatherEffectConfig = {
  [WeatherWarningType.HEAVY_RAIN]: {
    particles: {
      count: 3000,
      size: 0.1,
      color: '#33b5e5',
      opacity: 0.6,
      velocity: { x: 0, y: -0.8, z: 0 },
      lifespan: 5000
    },
    color: '#1e40af',
    intensity: 0.7
  },
  [WeatherWarningType.STRONG_WIND]: {
    particles: {
      count: 1500,
      size: 0.3,
      color: '#fbbf24',
      opacity: 0.5,
      velocity: { x: 0.5, y: 0.1, z: 0.2 },
      lifespan: 8000
    },
    color: '#f59e0b',
    intensity: 0.8,
    rotationSpeed: 0.02
  },
  [WeatherWarningType.GALE]: {
    particles: {
      count: 2000,
      size: 0.2,
      color: '#ef4444',
      opacity: 0.4,
      velocity: { x: 0.8, y: 0.2, z: 0.3 },
      lifespan: 6000
    },
    color: '#dc2626',
    intensity: 0.9,
    turbulence: 0.5
  },
  [WeatherWarningType.TORRENTIAL_RAIN]: {
    particles: {
      count: 5000,
      size: 0.15,
      color: '#1d4ed8',
      opacity: 0.8,
      velocity: { x: 0.1, y: -1.2, z: 0.1 },
      lifespan: 4000
    },
    color: '#1e3a8a',
    intensity: 1.0,
    density: 2.0
  }
}

// 默认场景配置
export const defaultSceneConfig: SceneConfig = {
  terrain: {
    size: 200,
    segments: 100,
    heightVariation: 5
  },
  lighting: {
    ambientIntensity: 0.5,
    directionalIntensity: 0.8,
    shadowMapSize: 2048
  },
  camera: {
    fov: 75,
    near: 0.1,
    far: 1000,
    initialPosition: { x: 0, y: 30, z: 100 }
  },
  fog: {
    color: '#87ceeb',
    near: 1,
    far: 1000
  }
}

// 预警级别颜色映射
export const warningLevelColors = {
  blue: '#3b82f6',
  yellow: '#eab308',
  orange: '#f97316',
  red: '#ef4444'
}

// 监测站点图标配置
export const stationIconConfig = {
  size: 2,
  color: '#10b981',
  hoverColor: '#059669',
  selectedColor: '#dc2626'
}
